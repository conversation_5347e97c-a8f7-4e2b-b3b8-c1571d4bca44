diff --git a/convex/files.ts b/convex/files.ts
index 8376f15..4c4e05a 100644
--- a/convex/files.ts
+++ b/convex/files.ts
@@ -1,7 +1,8 @@
+"use node";
 import { v } from "convex/values";
+import * as XLSX from "xlsx";
 import { mutation, query, action } from "./_generated/server";
 import { getAuthUserId } from "@convex-dev/auth/server";
-import { Id } from "./_generated/dataModel";
 
 // Generate upload URL for files
 export const generateUploadUrl = mutation({
@@ -118,6 +119,29 @@ export const extractTextFromFile = action({
         // CSV file
         const textDecoder = new TextDecoder();
         extractedText = textDecoder.decode(arrayBuffer);
+      } else if (
+        fileType.includes(
+          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
+        ) ||
+        fileType.includes("application/vnd.ms-excel") ||
+        fileType.includes("application/vnd.ms-excel.sheet.macroenabled.12")
+      ) {
+        // Excel file (.xlsx, .xls, .xlsm)
+        try {
+          const data = new Uint8Array(arrayBuffer);
+          const workbook = XLSX.read(data, { type: "array" });
+          let excelText = "";
+          workbook.SheetNames.forEach((sheetName) => {
+            const sheet = workbook.Sheets[sheetName];
+            const csvData = XLSX.utils.sheet_to_csv(sheet);
+            excelText += `--- Sheet: ${sheetName} ---\n${csvData}\n`;
+          });
+          extractedText = excelText;
+        } catch (excelErr) {
+          console.error("Excel parsing failed:", excelErr);
+          extractedText =
+            "[Excel file uploaded - failed to parse contents. Please provide a brief description of the spreadsheet if needed for the AI to process it.]";
+        }
       } else if (
         fileType.includes("text/markdown") ||
         fileType.includes("text/x-markdown")
diff --git a/package.json b/package.json
index cf7e39d..e652695 100644
--- a/package.json
+++ b/package.json
@@ -11,20 +11,20 @@
     "lint": "tsc -p convex -noEmit --pretty false && tsc -p . -noEmit --pretty false && convex dev --once && vite build"
   },
   "dependencies": {
-    "@ai-sdk/anthropic": "^2.0.0",
-    "@ai-sdk/cerebras": "^1.0.0",
-    "@ai-sdk/cohere": "^2.0.0",
-    "@ai-sdk/deepseek": "^1.0.0",
-    "@ai-sdk/google": "^2.0.0",
-    "@ai-sdk/groq": "^2.0.0",
-    "@ai-sdk/mistral": "^2.0.0",
-    "@ai-sdk/openai": "^2.0.0",
-    "@ai-sdk/openai-compatible": "^1.0.0",
+    "@ai-sdk/anthropic": "^2.0.1",
+    "@ai-sdk/cerebras": "^1.0.2",
+    "@ai-sdk/cohere": "^2.0.1",
+    "@ai-sdk/deepseek": "^1.0.2",
+    "@ai-sdk/google": "^2.0.2",
+    "@ai-sdk/groq": "^2.0.2",
+    "@ai-sdk/mistral": "^2.0.1",
+    "@ai-sdk/openai": "^2.0.4",
+    "@ai-sdk/openai-compatible": "^1.0.2",
     "@auth/core": "^0.40.0",
     "@convex-dev/auth": "^0.0.88",
-    "@convex-dev/resend": "^0.1.7",
+    "@convex-dev/resend": "^0.1.9",
     "@dmitryrechkin/json-schema-to-zod": "^1.0.1",
-    "@google/genai": "^1.12.0",
+    "@google/genai": "^1.13.0",
     "@modelcontextprotocol/sdk": "^1.17.1",
     "@openrouter/ai-sdk-provider": "^0.7.3",
     "@radix-ui/react-accordion": "^1.2.11",
@@ -46,26 +46,26 @@
     "@radix-ui/react-tooltip": "^1.2.7",
     "@react-email/body": "^0.1.0",
     "@react-email/button": "^0.2.0",
-    "@react-email/components": "^0.4.0",
+    "@react-email/components": "^0.5.0",
     "@react-email/container": "^0.0.15",
     "@react-email/head": "^0.0.12",
     "@react-email/hr": "^0.0.11",
     "@react-email/html": "^0.0.11",
     "@react-email/img": "^0.0.11",
     "@react-email/link": "^0.0.12",
-    "@react-email/render": "^1.1.4",
+    "@react-email/render": "^1.2.0",
     "@react-email/section": "^0.0.16",
     "@react-email/text": "^0.1.5",
     "@sentry/react": "^10.2.0",
     "@tailwindcss/typography": "^0.5.16",
-    "@tsparticles/engine": "^3.9.0",
+    "@tsparticles/engine": "^3.9.1",
     "@tsparticles/react": "^3.0.0",
     "@types/mathjs": "^9.4.2",
     "@types/react-syntax-highlighter": "^15.5.13",
     "@types/recharts": "^2.0.1",
     "@upstash/redis": "^1.35.3",
     "@vercel/analytics": "^1.5.0",
-    "ai": "^5.0.0",
+    "ai": "^5.0.5",
     "class-variance-authority": "^0.7.1",
     "clsx": "^2.1.1",
     "cmdk": "^1.1.1",
@@ -88,7 +88,7 @@
     "react-markdown": "^10.1.0",
     "react-syntax-highlighter": "^15.6.1",
     "react-tsparticles": "^2.12.2",
-    "recharts": "^3.1.0",
+    "recharts": "^3.1.2",
     "rehype-autolink-headings": "^7.1.0",
     "rehype-katex": "^7.0.1",
     "rehype-raw": "^7.0.0",
@@ -106,12 +106,13 @@
     "remark-unwrap-images": "^5.0.0",
     "remark-wiki-link": "^2.0.1",
     "shadcn-ui": "^0.9.5",
-    "sonner": "^2.0.6",
+    "sonner": "^2.0.7",
     "stripe": "^18.4.0",
     "tailwind-merge": "^3.3.1",
     "tailwindcss-animate": "^1.0.7",
-    "tsparticles": "^3.9.0",
-    "zod": "^4.0.14"
+    "tsparticles": "^3.9.1",
+    "zod": "^4.0.15",
+    "xlsx": "^0.18.5"
   },
   "overrides": {
     "react-is": "19.0.0"
@@ -119,7 +120,7 @@
   "devDependencies": {
     "@eslint/js": "^9.32.0",
     "@tailwindcss/postcss": "^4.1.11",
-    "@types/node": "^24.1.0",
+    "@types/node": "^24.2.0",
     "@types/node-fetch": "2",
     "@types/react": "^19.1.9",
     "@types/react-dom": "^19.1.7",
@@ -136,7 +137,7 @@
     "prettier": "^3.6.2",
     "tailwindcss": "~4",
     "typescript": "~5.9.2",
-    "typescript-eslint": "^8.38.0",
+    "typescript-eslint": "^8.39.0",
     "vite": "^7.0.6"
   },
   "pnpm": {
diff --git a/pnpm-lock.yaml b/pnpm-lock.yaml
index 045c38f..274a1e3 100644
--- a/pnpm-lock.yaml
+++ b/pnpm-lock.yaml
@@ -9,32 +9,32 @@ importers:
   .:
     dependencies:
       '@ai-sdk/anthropic':
-        specifier: ^2.0.0
-        version: 2.0.0(zod@4.0.14)
+        specifier: ^2.0.1
+        version: 2.0.1(zod@4.0.15)
       '@ai-sdk/cerebras':
-        specifier: ^1.0.0
-        version: 1.0.0(zod@4.0.14)
+        specifier: ^1.0.2
+        version: 1.0.2(zod@4.0.15)
       '@ai-sdk/cohere':
-        specifier: ^2.0.0
-        version: 2.0.0(zod@4.0.14)
+        specifier: ^2.0.1
+        version: 2.0.1(zod@4.0.15)
       '@ai-sdk/deepseek':
-        specifier: ^1.0.0
-        version: 1.0.0(zod@4.0.14)
+        specifier: ^1.0.2
+        version: 1.0.2(zod@4.0.15)
       '@ai-sdk/google':
-        specifier: ^2.0.0
-        version: 2.0.0(zod@4.0.14)
+        specifier: ^2.0.2
+        version: 2.0.2(zod@4.0.15)
       '@ai-sdk/groq':
-        specifier: ^2.0.0
-        version: 2.0.0(zod@4.0.14)
+        specifier: ^2.0.2
+        version: 2.0.2(zod@4.0.15)
       '@ai-sdk/mistral':
-        specifier: ^2.0.0
-        version: 2.0.0(zod@4.0.14)
+        specifier: ^2.0.1
+        version: 2.0.1(zod@4.0.15)
       '@ai-sdk/openai':
-        specifier: ^2.0.0
-        version: 2.0.0(zod@4.0.14)
+        specifier: ^2.0.4
+        version: 2.0.4(zod@4.0.15)
       '@ai-sdk/openai-compatible':
-        specifier: ^1.0.0
-        version: 1.0.0(zod@4.0.14)
+        specifier: ^1.0.2
+        version: 1.0.2(zod@4.0.15)
       '@auth/core':
         specifier: ^0.40.0
         version: 0.40.0
@@ -42,20 +42,20 @@ importers:
         specifier: ^0.0.88
         version: 0.0.88(@auth/core@0.40.0)(convex@1.25.4(react@19.1.1))(react@19.1.1)
       '@convex-dev/resend':
-        specifier: ^0.1.7
-        version: 0.1.7(convex-helpers@0.1.100(@standard-schema/spec@1.0.0)(convex@1.25.4(react@19.1.1))(react@19.1.1)(typescript@5.9.2)(zod@4.0.14))(convex@1.25.4(react@19.1.1))(react@19.1.1)
+        specifier: ^0.1.9
+        version: 0.1.9(convex-helpers@0.1.100(@standard-schema/spec@1.0.0)(convex@1.25.4(react@19.1.1))(react@19.1.1)(typescript@5.9.2)(zod@4.0.15))(convex@1.25.4(react@19.1.1))(react@19.1.1)
       '@dmitryrechkin/json-schema-to-zod':
         specifier: ^1.0.1
         version: 1.0.1
       '@google/genai':
-        specifier: ^1.12.0
-        version: 1.12.0(@modelcontextprotocol/sdk@1.17.1)
+        specifier: ^1.13.0
+        version: 1.13.0(@modelcontextprotocol/sdk@1.17.1)
       '@modelcontextprotocol/sdk':
         specifier: ^1.17.1
         version: 1.17.1
       '@openrouter/ai-sdk-provider':
         specifier: ^0.7.3
-        version: 0.7.3(ai@5.0.0(zod@4.0.14))(zod@4.0.14)
+        version: 0.7.3(ai@5.0.5(zod@4.0.15))(zod@4.0.15)
       '@radix-ui/react-accordion':
         specifier: ^1.2.11
         version: 1.2.11(@types/react-dom@19.1.7(@types/react@19.1.9))(@types/react@19.1.9)(react-dom@19.1.1(react@19.1.1))(react@19.1.1)
@@ -114,8 +114,8 @@ importers:
         specifier: ^0.2.0
         version: 0.2.0(react@19.1.1)
       '@react-email/components':
-        specifier: ^0.4.0
-        version: 0.4.0(react-dom@19.1.1(react@19.1.1))(react@19.1.1)
+        specifier: ^0.5.0
+        version: 0.5.0(react-dom@19.1.1(react@19.1.1))(react@19.1.1)
       '@react-email/container':
         specifier: ^0.0.15
         version: 0.0.15(react@19.1.1)
@@ -135,8 +135,8 @@ importers:
         specifier: ^0.0.12
         version: 0.0.12(react@19.1.1)
       '@react-email/render':
-        specifier: ^1.1.4
-        version: 1.1.4(react-dom@19.1.1(react@19.1.1))(react@19.1.1)
+        specifier: ^1.2.0
+        version: 1.2.0(react-dom@19.1.1(react@19.1.1))(react@19.1.1)
       '@react-email/section':
         specifier: ^0.0.16
         version: 0.0.16(react@19.1.1)
@@ -150,11 +150,11 @@ importers:
         specifier: ^0.5.16
         version: 0.5.16(tailwindcss@4.1.11)
       '@tsparticles/engine':
-        specifier: ^3.9.0
-        version: 3.9.0
+        specifier: ^3.9.1
+        version: 3.9.1
       '@tsparticles/react':
         specifier: ^3.0.0
-        version: 3.0.0(@tsparticles/engine@3.9.0)(react-dom@19.1.1(react@19.1.1))(react@19.1.1)
+        version: 3.0.0(@tsparticles/engine@3.9.1)(react-dom@19.1.1(react@19.1.1))(react@19.1.1)
       '@types/mathjs':
         specifier: ^9.4.2
         version: 9.4.2
@@ -171,8 +171,8 @@ importers:
         specifier: ^1.5.0
         version: 1.5.0(react@19.1.1)
       ai:
-        specifier: ^5.0.0
-        version: 5.0.0(zod@4.0.14)
+        specifier: ^5.0.5
+        version: 5.0.5(zod@4.0.15)
       class-variance-authority:
         specifier: ^0.7.1
         version: 0.7.1
@@ -240,8 +240,8 @@ importers:
         specifier: ^2.12.2
         version: 2.12.2(react@19.1.1)
       recharts:
-        specifier: ^3.1.0
-        version: 3.1.0(@types/react@19.1.9)(react-dom@19.1.1(react@19.1.1))(react-is@18.3.1)(react@19.1.1)(redux@5.0.1)
+        specifier: ^3.1.2
+        version: 3.1.2(@types/react@19.1.9)(react-dom@19.1.1(react@19.1.1))(react-is@18.3.1)(react@19.1.1)(redux@5.0.1)
       rehype-autolink-headings:
         specifier: ^7.1.0
         version: 7.1.0
@@ -294,11 +294,11 @@ importers:
         specifier: ^0.9.5
         version: 0.9.5
       sonner:
-        specifier: ^2.0.6
-        version: 2.0.6(react-dom@19.1.1(react@19.1.1))(react@19.1.1)
+        specifier: ^2.0.7
+        version: 2.0.7(react-dom@19.1.1(react@19.1.1))(react@19.1.1)
       stripe:
         specifier: ^18.4.0
-        version: 18.4.0(@types/node@24.1.0)
+        version: 18.4.0(@types/node@24.2.0)
       tailwind-merge:
         specifier: ^3.3.1
         version: 3.3.1
@@ -306,11 +306,14 @@ importers:
         specifier: ^1.0.7
         version: 1.0.7(tailwindcss@4.1.11)
       tsparticles:
-        specifier: ^3.9.0
-        version: 3.9.0
+        specifier: ^3.9.1
+        version: 3.9.1
+      xlsx:
+        specifier: ^0.18.5
+        version: 0.18.5
       zod:
-        specifier: ^4.0.14
-        version: 4.0.14
+        specifier: ^4.0.15
+        version: 4.0.15
     devDependencies:
       '@eslint/js':
         specifier: ^9.32.0
@@ -319,8 +322,8 @@ importers:
         specifier: ^4.1.11
         version: 4.1.11
       '@types/node':
-        specifier: ^24.1.0
-        version: 24.1.0
+        specifier: ^24.2.0
+        version: 24.2.0
       '@types/node-fetch':
         specifier: '2'
         version: 2.6.12
@@ -332,7 +335,7 @@ importers:
         version: 19.1.7(@types/react@19.1.9)
       '@vitejs/plugin-react':
         specifier: ^4.7.0
-        version: 4.7.0(vite@7.0.6(@types/node@24.1.0)(jiti@2.4.2)(lightningcss@1.30.1)(yaml@2.8.0))
+        version: 4.7.0(vite@7.0.6(@types/node@24.2.0)(jiti@2.4.2)(lightningcss@1.30.1)(yaml@2.8.0))
       '@welldone-software/why-did-you-render':
         specifier: ^10.0.1
         version: 10.0.1(react@19.1.1)
@@ -370,70 +373,70 @@ importers:
         specifier: ~5.9.2
         version: 5.9.2
       typescript-eslint:
-        specifier: ^8.38.0
-        version: 8.38.0(eslint@9.32.0(jiti@2.4.2))(typescript@5.9.2)
+        specifier: ^8.39.0
+        version: 8.39.0(eslint@9.32.0(jiti@2.4.2))(typescript@5.9.2)
       vite:
         specifier: ^7.0.6
-        version: 7.0.6(@types/node@24.1.0)(jiti@2.4.2)(lightningcss@1.30.1)(yaml@2.8.0)
+        version: 7.0.6(@types/node@24.2.0)(jiti@2.4.2)(lightningcss@1.30.1)(yaml@2.8.0)
 
 packages:
 
-  '@ai-sdk/anthropic@2.0.0':
-    resolution: {integrity: sha512-uyyaO4KhxoIKZztREqLPh+6/K3ZJx/rp72JKoUEL9/kC+vfQTThUfPnY/bUryUpcnawx8IY/tSoYNOi/8PCv7w==}
+  '@ai-sdk/anthropic@2.0.1':
+    resolution: {integrity: sha512-HtNbpNV9qXQosHu00+CBMEcdTerwZY+kpVMNak0xP/P5TF6XkPf7IyizhLuc7y5zcXMjZCMA7jDGkcEdZCEdkw==}
     engines: {node: '>=18'}
     peerDependencies:
       zod: ^3.25.76 || ^4
 
-  '@ai-sdk/cerebras@1.0.0':
-    resolution: {integrity: sha512-kUwitfAzwbdXpinT6jw26YDGK+Jy5jpaNIscnIVHE08UD6b0MXjSVsktiB8fXnchbD+ilpOaOl9wN21ILtKeog==}
+  '@ai-sdk/cerebras@1.0.2':
+    resolution: {integrity: sha512-LzLpLcYkpeTSSG7t56Pc51wv2nfP3ka/P28DbHC2KNnWe7/AoPHrY3DmHXLHU/JSxg1nnmh/0rp02pBedcPNSQ==}
     engines: {node: '>=18'}
     peerDependencies:
       zod: ^3.25.76 || ^4
 
-  '@ai-sdk/cohere@2.0.0':
-    resolution: {integrity: sha512-nn4iNkwYm3d32nYCpss+oS5urjxwyRXeqiBuONSYcIZ4VguLirykZ8cpl6ebtjaQJ08JKzljVf2xVHbPZw+rnw==}
+  '@ai-sdk/cohere@2.0.1':
+    resolution: {integrity: sha512-EvI3T4j+9Os5RG6EQy/l0C4ocRpoBKQk+Pp6ModzS640tVZ+RCxx3deYrQx8cJ6GIaztVM7Y5fvq2nsOxJdYSQ==}
     engines: {node: '>=18'}
     peerDependencies:
       zod: ^3.25.76 || ^4
 
-  '@ai-sdk/deepseek@1.0.0':
-    resolution: {integrity: sha512-5rumr8Gn41MT+mYD6PxPt2vMTh8h4S1KOcJDL1s8+qFaPSqgGal58BzwrVKUS/ycMM61XR6ymX25x09QLAteYw==}
+  '@ai-sdk/deepseek@1.0.2':
+    resolution: {integrity: sha512-9dVgQqPWRNkwvugcMNSfOy/btBMX/MFj6UUakNG8btWICAvdcxBUm3BLElYXfsB4jocCX1ypqZmOvn1VHqX9Gg==}
     engines: {node: '>=18'}
     peerDependencies:
       zod: ^3.25.76 || ^4
 
-  '@ai-sdk/gateway@1.0.0':
-    resolution: {integrity: sha512-VEm87DyRx1yIPywbTy8ntoyh4jEDv1rJ88m+2I7zOm08jJI5BhFtAWh0OF6YzZu1Vu4NxhOWO4ssGdsqydDQ3A==}
+  '@ai-sdk/gateway@1.0.3':
+    resolution: {integrity: sha512-QRGz2vH1WR9NvCv8gWocoebAKiXcuqj22mug6i8COeVsp33x5K5cK2DT4TwiQx5SfYbqJbVoBT+UqnHF7A3PHA==}
     engines: {node: '>=18'}
     peerDependencies:
       zod: ^3.25.76 || ^4
 
-  '@ai-sdk/google@2.0.0':
-    resolution: {integrity: sha512-35uWKG+aWm0QClJV/kNhcyR9IVrDkZoI1UlWvUCjwoqbCxj4/L/1LKKbpM3JSRa9u74ghHzBB0UjLHdgcIoanw==}
+  '@ai-sdk/google@2.0.2':
+    resolution: {integrity: sha512-ZTETUnuXPBErzRiXSFvPjRUJQ6kAZLueFi3qCtpxPe7xgmQAqY+0z4pR+v4zOBgrCtgpuB6nayXXXG/8zUaCjA==}
     engines: {node: '>=18'}
     peerDependencies:
       zod: ^3.25.76 || ^4
 
-  '@ai-sdk/groq@2.0.0':
-    resolution: {integrity: sha512-3N1Rcg6wWaT8Vugqcg6N6PrEHI2NlunFqFQKJ12f37VZSXc4BL5G29PqJfrD9mOUP9h2f84wPiArg1py8Uhmmg==}
+  '@ai-sdk/groq@2.0.2':
+    resolution: {integrity: sha512-oiMkruBlXdCN8jPHr0bzZlPB5XCse0daQW8qPcnqx2gCARsAmM1xhoZdpWXxlBPwCueOjkqo1AGA7npKlnzC2Q==}
     engines: {node: '>=18'}
     peerDependencies:
       zod: ^3.25.76 || ^4
 
-  '@ai-sdk/mistral@2.0.0':
-    resolution: {integrity: sha512-3wjTl72Erh/hSWseJ8EttY23zQ+DaB/RO1RXGBg9FP86S/6cfXvpRtPKsr7/hlVyvD3WCfHP2bjlapbEkH60Ew==}
+  '@ai-sdk/mistral@2.0.1':
+    resolution: {integrity: sha512-GuX/5JXMiiTRBYIl3VJSUQQ3Zd6r/StnA94U/nkOL3Zd36LAhaM+RTTfumwj0BDgE2ou4OXiGD9qHn7V9+0dUw==}
     engines: {node: '>=18'}
     peerDependencies:
       zod: ^3.25.76 || ^4
 
-  '@ai-sdk/openai-compatible@1.0.0':
-    resolution: {integrity: sha512-I5d29iB82ty/e0jqpH4iiWCkFtG1oANzK2WwY3WNF1vWjzqVzvPT+YXR0y43caiq0lIWwMyhJu+bMizmzigrwA==}
+  '@ai-sdk/openai-compatible@1.0.2':
+    resolution: {integrity: sha512-VqTEzo1ueUsS9FGHtyoAHK11nPIgtziwwGGy5qtGOs+JRcZVEdPqcWe1n2+Ichl4edchoAHo/tygAymaiom9mg==}
     engines: {node: '>=18'}
     peerDependencies:
       zod: ^3.25.76 || ^4
 
-  '@ai-sdk/openai@2.0.0':
-    resolution: {integrity: sha512-G0WY5K81JwGpuX9HEmP2VTdt3N9m43qPnGT4fWkXcpu6Y2B05nnjs8k1r/csCJd8+TkYC6esjBABQYHxdMOejQ==}
+  '@ai-sdk/openai@2.0.4':
+    resolution: {integrity: sha512-PnVUFosX2zWk6emiX4SulZrGvGw79YJjh/DgxahpAcBpz71GlQyzVNTFAQx7ycFGLBg/YniJr1M2WJybYZZ3ug==}
     engines: {node: '>=18'}
     peerDependencies:
       zod: ^3.25.76 || ^4
@@ -444,8 +447,8 @@ packages:
     peerDependencies:
       zod: ^3.23.8
 
-  '@ai-sdk/provider-utils@3.0.0':
-    resolution: {integrity: sha512-BoQZtGcBxkeSH1zK+SRYNDtJPIPpacTeiMZqnG4Rv6xXjEwM0FH4MGs9c+PlhyEWmQCzjRM2HAotEydFhD4dYw==}
+  '@ai-sdk/provider-utils@3.0.1':
+    resolution: {integrity: sha512-/iP1sKc6UdJgGH98OCly7sWJKv+J9G47PnTjIj40IJMUQKwDrUMyf7zOOfRtPwSuNifYhSoJQ4s1WltI65gJ/g==}
     engines: {node: '>=18'}
     peerDependencies:
       zod: ^3.25.76 || ^4
@@ -620,8 +623,8 @@ packages:
       react:
         optional: true
 
-  '@convex-dev/resend@0.1.7':
-    resolution: {integrity: sha512-7OXY6DfC/qy3CjMBHA9H/JikjrUNpZFSS4B1sVAzQfQvVElThqQAj/E2h9Z4mDhwRv5bxGgJfckHyyP+u+ZSRA==}
+  '@convex-dev/resend@0.1.9':
+    resolution: {integrity: sha512-/sNP/h22q53iGSBmo8FRYZz0jN3uSWUr8Manjsox6mPeFHhTEgFR0hSahTk27SJeOAA1UA9lkMPD3gBBpUVxKw==}
     peerDependencies:
       convex: ^1.23.0
       convex-helpers: ^0.1.99
@@ -992,8 +995,8 @@ packages:
   '@floating-ui/utils@0.2.9':
     resolution: {integrity: sha512-MDWhGtE+eHw5JW7lq4qhc5yRLS11ERl1c7Z6Xd0a58DozHES6EnNNwUWbMiG4J9Cgj053Bhk8zvlhFYKVhULwg==}
 
-  '@google/genai@1.12.0':
-    resolution: {integrity: sha512-JBkQsULVexdM9zY4iXbm3A2dJ7El/hSPGCnxuRWPJNgeqcfYuyUnPTSy+I/v+MvTbz/occVmONSD6wn+17QLkg==}
+  '@google/genai@1.13.0':
+    resolution: {integrity: sha512-BxilXzE8cJ0zt5/lXk6KwuBcIT9P2Lbi2WXhwWMbxf1RNeC68/8DmYQqMrzQP333CieRMdbDXs0eNCphLoScWg==}
     engines: {node: '>=20.0.0'}
     peerDependencies:
       '@modelcontextprotocol/sdk': ^1.11.0
@@ -1731,8 +1734,8 @@ packages:
     peerDependencies:
       react: ^18.0 || ^19.0 || ^19.0.0-rc
 
-  '@react-email/components@0.4.0':
-    resolution: {integrity: sha512-JzEb6eVXizGfypXbr4jRj9wthywJUN62PrR7Q1Cz6TChi1FClIhIox0EpL9Asx0WpLHYB/XA7D6zjHASMeef3A==}
+  '@react-email/components@0.5.0':
+    resolution: {integrity: sha512-esRbP+yMmSkNP9hcpiy2RwpDnvSmlxJcJ1HHbzSwlACGlCHTap+ma344QovvzhpVRhMccyWemdClLG822UvVpQ==}
     engines: {node: '>=18.0.0'}
     peerDependencies:
       react: ^18.0 || ^19.0 || ^19.0.0-rc
@@ -1796,8 +1799,8 @@ packages:
     peerDependencies:
       react: ^18.0 || ^19.0 || ^19.0.0-rc
 
-  '@react-email/render@1.1.4':
-    resolution: {integrity: sha512-9ZFRrDB8AiRpacWDDXC5q14D5uCE1uR7iStbxAOHsL5vvAj8JGfCwl8zZ/BubVwALlIhFQiyJPCvGbyfbkPVuw==}
+  '@react-email/render@1.2.0':
+    resolution: {integrity: sha512-5fpbV16VYR9Fmk8t7xiwPNAjxjdI8XzVtlx9J9OkhOsIHdr2s5DwAj8/MXzWa9qRYJyLirQ/l7rBSjjgyRAomw==}
     engines: {node: '>=18.0.0'}
     peerDependencies:
       react: ^18.0 || ^19.0 || ^19.0.0-rc
@@ -2080,86 +2083,83 @@ packages:
     peerDependencies:
       tailwindcss: '>=3.0.0 || insiders || >=4.0.0-alpha.20 || >=4.0.0-beta.1'
 
-  '@tsparticles/basic@3.9.0':
-    resolution: {integrity: sha512-L69wL7kCm103ZiYPXdqIivQIFSEq5eyIGxqfQAbhvwzSs5kwK+bro4RMgzZQRzSysODZ5oi/wMhbHK8Knt7TsA==}
-
-  '@tsparticles/engine@3.8.1':
-    resolution: {integrity: sha512-S8h10nuZfElY7oih//NUHnT5qf4v3/dnsU8CMs7dz5lBEGr3amrYrXk0V+YKPTIQwfdmJHUaSBoAqFiv4aEGIA==}
+  '@tsparticles/basic@3.9.1':
+    resolution: {integrity: sha512-ijr2dHMx0IQHqhKW3qA8tfwrR2XYbbWYdaJMQuBo2CkwBVIhZ76U+H20Y492j/NXpd1FUnt2aC0l4CEVGVGdeQ==}
 
-  '@tsparticles/engine@3.9.0':
-    resolution: {integrity: sha512-/LgIE67s18ROFEG/s0WX/M1qtjYuDYThUCeTQ2aOhb9hmJJo8PFCODP9pA3jEqZwMyk/8j5eiEtOneJ/9VDyMA==}
+  '@tsparticles/engine@3.9.1':
+    resolution: {integrity: sha512-DpdgAhWMZ3Eh2gyxik8FXS6BKZ8vyea+Eu5BC4epsahqTGY9V3JGGJcXC6lRJx6cPMAx1A0FaQAojPF3v6rkmQ==}
 
-  '@tsparticles/interaction-external-attract@3.9.0':
-    resolution: {integrity: sha512-H8xD3VxyDNME3JaLUBZCluNG2dS89bB8tFjuGsa0jaADDO2qIA4jE/l6SCUsJmDDRevgUgUgmm0rXIQACxMong==}
+  '@tsparticles/interaction-external-attract@3.9.1':
+    resolution: {integrity: sha512-5AJGmhzM9o4AVFV24WH5vSqMBzOXEOzIdGLIr+QJf4fRh9ZK62snsusv/ozKgs2KteRYQx+L7c5V3TqcDy2upg==}
 
-  '@tsparticles/interaction-external-bounce@3.9.0':
-    resolution: {integrity: sha512-hahevKmBYd5aEz7qMx5jlT4NKlO+Y+3XGk3ryltPIzUp/+MfApulpnmvAAiDLG7QDGqc6QK4Xhqe8jZO0iitUQ==}
+  '@tsparticles/interaction-external-bounce@3.9.1':
+    resolution: {integrity: sha512-bv05+h70UIHOTWeTsTI1AeAmX6R3s8nnY74Ea6p6AbQjERzPYIa0XY19nq/hA7+Nrg+EissP5zgoYYeSphr85A==}
 
-  '@tsparticles/interaction-external-bubble@3.9.0':
-    resolution: {integrity: sha512-TVGuSdAGZWyTOjRXlRiduY2+2HxoN7HMCZ8JQjQVYnjnWjiLuSVq3UKrOCibFVglanTvmds2w0AF6cGz65KxOA==}
+  '@tsparticles/interaction-external-bubble@3.9.1':
+    resolution: {integrity: sha512-tbd8ox/1GPl+zr+KyHQVV1bW88GE7OM6i4zql801YIlCDrl9wgTDdDFGIy9X7/cwTvTrCePhrfvdkUamXIribQ==}
 
-  '@tsparticles/interaction-external-connect@3.9.0':
-    resolution: {integrity: sha512-RK+JOqR8kRHDie6V4R9orB7oY0YPlRU/ADp0wfhEtIqiice56fYczq4375tsajwnLgNcUDsDBdC8eNELoEMfog==}
+  '@tsparticles/interaction-external-connect@3.9.1':
+    resolution: {integrity: sha512-sq8YfUNsIORjXHzzW7/AJQtfi/qDqLnYG2qOSE1WOsog39MD30RzmiOloejOkfNeUdcGUcfsDgpUuL3UhzFUOA==}
 
-  '@tsparticles/interaction-external-grab@3.9.0':
-    resolution: {integrity: sha512-nxD+3hnJmPNtuT8YnG6af29gb7k17C6DuIc4k74e9QmGf0zl+OPbQfi+DkIMdkRaLd0MRZtXRjK5vG2rpf8GmA==}
+  '@tsparticles/interaction-external-grab@3.9.1':
+    resolution: {integrity: sha512-QwXza+sMMWDaMiFxd8y2tJwUK6c+nNw554+/9+tEZeTTk2fCbB0IJ7p/TH6ZGWDL0vo2muK54Njv2fEey191ow==}
 
-  '@tsparticles/interaction-external-pause@3.9.0':
-    resolution: {integrity: sha512-+rrHoTv7nPs5Fg+tG+vie6XKhyJMnfjCU/vuB5AoSzggmpy6NzYfUBQchlWX76krakwzfAiUIf5eF2egiKwfSg==}
+  '@tsparticles/interaction-external-pause@3.9.1':
+    resolution: {integrity: sha512-Gzv4/FeNir0U/tVM9zQCqV1k+IAgaFjDU3T30M1AeAsNGh/rCITV2wnT7TOGFkbcla27m4Yxa+Fuab8+8pzm+g==}
 
-  '@tsparticles/interaction-external-push@3.9.0':
-    resolution: {integrity: sha512-802MbjjE+lYs+kZLpZlEzLoYXRAjRIgxLPfB29bUDpEEoJwC47McV7v3x1UTOll8eIRrn5zIkR0QRqHh7C4hCg==}
+  '@tsparticles/interaction-external-push@3.9.1':
+    resolution: {integrity: sha512-GvnWF9Qy4YkZdx+WJL2iy9IcgLvzOIu3K7aLYJFsQPaxT8d9TF8WlpoMlWKnJID6H5q4JqQuMRKRyWH8aAKyQw==}
 
-  '@tsparticles/interaction-external-remove@3.9.0':
-    resolution: {integrity: sha512-hcSxEAqD6oxACzTxEFXcZMmE49/HeOPXYeQlWAS2jH+s4MDJjW1UXABPjltRk5dMrjLz/yx4IsjUO5DOT9oTgg==}
+  '@tsparticles/interaction-external-remove@3.9.1':
+    resolution: {integrity: sha512-yPThm4UDWejDOWW5Qc8KnnS2EfSo5VFcJUQDWc1+Wcj17xe7vdSoiwwOORM0PmNBzdDpSKQrte/gUnoqaUMwOA==}
 
-  '@tsparticles/interaction-external-repulse@3.9.0':
-    resolution: {integrity: sha512-EttzcdLbwwj9n9aAVAipZ8Gy6QVxZqBo4NdTULG9JYqWn5rauQCbdo87aeBUljyr2iAXxVq3hQ5RYnICG1FWQQ==}
+  '@tsparticles/interaction-external-repulse@3.9.1':
+    resolution: {integrity: sha512-/LBppXkrMdvLHlEKWC7IykFhzrz+9nebT2fwSSFXK4plEBxDlIwnkDxd3FbVOAbnBvx4+L8+fbrEx+RvC8diAw==}
 
-  '@tsparticles/interaction-external-slow@3.9.0':
-    resolution: {integrity: sha512-t3Lnv7ElArETJT9ZpcjRxb0vBynYVuaLaEZ/CflHekY+EQVDfDDQnGM51+IuTdVPXpGZkmzlDVHazl4sK/kmVQ==}
+  '@tsparticles/interaction-external-slow@3.9.1':
+    resolution: {integrity: sha512-1ZYIR/udBwA9MdSCfgADsbDXKSFS0FMWuPWz7bm79g3sUxcYkihn+/hDhc6GXvNNR46V1ocJjrj0u6pAynS1KQ==}
 
-  '@tsparticles/interaction-external-trail@3.8.1':
-    resolution: {integrity: sha512-CUiTxCtTASYdqi55KpJ98IRnuLS+G66v+s1/dZeAE7F7wzL7tkcUBQ3hP1yzBpFmKsoYHJfz9nAfocAkgRb6hg==}
+  '@tsparticles/interaction-external-trail@3.9.1':
+    resolution: {integrity: sha512-Au0v2oiqfKTemI/4bzjD4dUXzIngB5Q2T4nJcMCYpP24uZfwZh5xTjUMH7gyJyyaRTdMl9IJrp8ySjyYbLfeGg==}
 
-  '@tsparticles/interaction-particles-attract@3.9.0':
-    resolution: {integrity: sha512-bOhxSVDDGlkfP5FgsUN42Oj8wVX68hr88BimnZyF89uZAQLysj8XqlcjHbHuhEViGLsugi1J1jKPPOMbk935+g==}
+  '@tsparticles/interaction-particles-attract@3.9.1':
+    resolution: {integrity: sha512-CYYYowJuGwRLUixQcSU/48PTKM8fCUYThe0hXwQ+yRMLAn053VHzL7NNZzKqEIeEyt5oJoy9KcvubjKWbzMBLQ==}
 
-  '@tsparticles/interaction-particles-collisions@3.9.0':
-    resolution: {integrity: sha512-8LN84jM2DlBvPR3RkSODHeSSqoboq6/cdGXWhSoF7bSL6LFcCV8qa/mICsbR/JdT9l2r9chUMJlrG9QpTyOZGA==}
+  '@tsparticles/interaction-particles-collisions@3.9.1':
+    resolution: {integrity: sha512-ggGyjW/3v1yxvYW1IF1EMT15M6w31y5zfNNUPkqd/IXRNPYvm0Z0ayhp+FKmz70M5p0UxxPIQHTvAv9Jqnuj8w==}
 
-  '@tsparticles/interaction-particles-links@3.9.0':
-    resolution: {integrity: sha512-52aH4+LQJBOeUskt+ZEUdpVUg7nUpNA04ThERNm/kcsdcm1G4JAuAadujnuARi8wHhEt3Q35ufxX32/IyFJiQA==}
+  '@tsparticles/interaction-particles-links@3.9.1':
+    resolution: {integrity: sha512-MsLbMjy1vY5M5/hu/oa5OSRZAUz49H3+9EBMTIOThiX+a+vpl3sxc9AqNd9gMsPbM4WJlub8T6VBZdyvzez1Vg==}
 
-  '@tsparticles/move-base@3.9.0':
-    resolution: {integrity: sha512-5zt2B46nfokJOjZe2xwgEXkH0Lh8ZYIWp8UmLCFsnEEU2+AqpeUT+guDG8iARMKomgVenxFPjlwlDfAHPMQMSg==}
+  '@tsparticles/move-base@3.9.1':
+    resolution: {integrity: sha512-X4huBS27d8srpxwOxliWPUt+NtCwY+8q/cx1DvQxyqmTA8VFCGpcHNwtqiN+9JicgzOvSuaORVqUgwlsc7h4pQ==}
 
-  '@tsparticles/move-parallax@3.9.0':
-    resolution: {integrity: sha512-E6vfXdVMSMycdHBqBzPxVf5OJT2kiPl/ZfdQaSkS2PPAbLYT1xhlyMkejgp/Gf0C4/4qQjuiVHjzQCqELAviAw==}
+  '@tsparticles/move-parallax@3.9.1':
+    resolution: {integrity: sha512-whlOR0bVeyh6J/hvxf/QM3DqvNnITMiAQ0kro6saqSDItAVqg4pYxBfEsSOKq7EhjxNvfhhqR+pFMhp06zoCVA==}
 
-  '@tsparticles/plugin-absorbers@3.9.0':
-    resolution: {integrity: sha512-52k5fbijPebH8Qbh/53NevNdkiluy734WYWNwW9526OgoWGgAVSp3+CNV9PHEiVq+RwtIsb2DtJEF9tYE4MfOA==}
+  '@tsparticles/plugin-absorbers@3.9.1':
+    resolution: {integrity: sha512-q9SQllpbPPgw1+euxHPYCFawOVUazQkkwnleiIgpYSiimlCyjIdwGnFPSNe1Sypzqmr2h6oOyX2vkK5ZVNEu8A==}
 
-  '@tsparticles/plugin-easing-quad@3.9.0':
-    resolution: {integrity: sha512-FXBr/eNDSnJFtUZbc5tniKo8evdhUD88YiCQKAeOZelooXJVJs6JdYj+GmU2corMtvyWDb5pNEQhWLIneiS23A==}
+  '@tsparticles/plugin-easing-quad@3.9.1':
+    resolution: {integrity: sha512-C2UJOca5MTDXKUTBXj30Kiqr5UyID+xrY/LxicVWWZPczQW2bBxbIbfq9ULvzGDwBTxE2rdvIB8YFKmDYO45qw==}
 
-  '@tsparticles/plugin-emitters-shape-circle@3.9.0':
-    resolution: {integrity: sha512-pfmLCsdgcrK598Ky9COIdCkmus0NcCF+rrSahIkcDRgMmdn5EC7nEk8+fN+ab5fVeAoKyy8wUoov4TMHHVnuiA==}
+  '@tsparticles/plugin-emitters-shape-circle@3.9.1':
+    resolution: {integrity: sha512-z+9MsAPWr++sNz6N6303rRDjusW0BIPhHY51E5eXGDcRdOqrESDs6y99AJ/6Kdb/PpibCIYjFY9jVi2JJADPRA==}
 
-  '@tsparticles/plugin-emitters-shape-square@3.9.0':
-    resolution: {integrity: sha512-fsAdT01kWAwYelGTPsHZJfdYFnqrf0gVHvWSD2f49pIyWd5KI0HeY64LOjwJeXzwqkG4+WBDDEMNZvsLstzVzg==}
+  '@tsparticles/plugin-emitters-shape-square@3.9.1':
+    resolution: {integrity: sha512-dhA1c7FKs19B8lgTf25OTA3JoptNA+rjorsqCFuY1BZDI8g9E8DNqikUge14/W7nZN96+98hY+ghxSl4K2YsgA==}
 
-  '@tsparticles/plugin-emitters@3.9.0':
-    resolution: {integrity: sha512-XbOJmt4Iez0SXTVC5k8oo4p5x6/EJoFFesVtKhtfoCmeVkXZAdQotonfqUIfe6ltDtpOVBK9oKNtA0rIvPviNA==}
+  '@tsparticles/plugin-emitters@3.9.1':
+    resolution: {integrity: sha512-h7opR8SoFWBmVHceDLJUerLENaPfkJSh2zQYvzmLj2L+V3VLS1QDgty+4QZVeZfqNROmgQw2eLFA5El1E0sqqw==}
 
-  '@tsparticles/plugin-hex-color@3.9.0':
-    resolution: {integrity: sha512-hV6Rsdv4iR1/zx9glMY8IJLcG19ucMw6Hgm3GcFy7zg6sO2nQReVOllUrab06nS1FthxNCuCxtOmt7v3n0oHpA==}
+  '@tsparticles/plugin-hex-color@3.9.1':
+    resolution: {integrity: sha512-vZgZ12AjUicJvk7AX4K2eAmKEQX/D1VEjEPFhyjbgI7A65eX72M465vVKIgNA6QArLZ1DLs7Z787LOE6GOBWsg==}
 
-  '@tsparticles/plugin-hsl-color@3.9.0':
-    resolution: {integrity: sha512-prN/nUz3bqc47fzZvcaYl2F+PBoOOc9UU/0IKZizBMX4o7f6HWDbgNZ6BtP40xACoSBwSGO67k6pMee89kSXNg==}
+  '@tsparticles/plugin-hsl-color@3.9.1':
+    resolution: {integrity: sha512-jJd1iGgRwX6eeNjc1zUXiJivaqC5UE+SC2A3/NtHwwoQrkfxGWmRHOsVyLnOBRcCPgBp/FpdDe6DIDjCMO715w==}
 
-  '@tsparticles/plugin-rgb-color@3.9.0':
-    resolution: {integrity: sha512-jN5mdTGfkNIlwfqW9QlL11Pwc49iZM8mIdxfGqsMQLqbNQ4ii6H/03YSVxyv9yvXR6Aq8Uw8a8D2OQTUIS4E5g==}
+  '@tsparticles/plugin-rgb-color@3.9.1':
+    resolution: {integrity: sha512-SBxk7f1KBfXeTnnklbE2Hx4jBgh6I6HOtxb+Os1gTp0oaghZOkWcCD2dP4QbUu7fVNCMOcApPoMNC8RTFcy9wQ==}
 
   '@tsparticles/react@3.0.0':
     resolution: {integrity: sha512-hjGEtTT1cwv6BcjL+GcVgH++KYs52bIuQGW3PWv7z3tMa8g0bd6RI/vWSLj7p//NZ3uTjEIeilYIUPBh7Jfq/Q==}
@@ -2168,68 +2168,68 @@ packages:
       react: '>=16.8.0'
       react-dom: '>=16.8.0'
 
-  '@tsparticles/shape-circle@3.9.0':
-    resolution: {integrity: sha512-ga5U59DM7w1OwRDZda4wY5QTA+3sTUy4u/L7+JlwzjbWuBE7UAzpqZ1tE0Vvu4fOLsN8kRkEN5Mw1dxSOilpTg==}
+  '@tsparticles/shape-circle@3.9.1':
+    resolution: {integrity: sha512-DqZFLjbuhVn99WJ+A9ajz9YON72RtCcvubzq6qfjFmtwAK7frvQeb6iDTp6Ze9FUipluxVZWVRG4vWTxi2B+/g==}
 
-  '@tsparticles/shape-emoji@3.9.0':
-    resolution: {integrity: sha512-oHDAsE+bybUxOar/Wp9MSEp0NUE/5MtyEiurZzHxoCgLQkpcHEjoSg2cvP9JHUfH3m+z0YcdG36eQM/4h3CnSw==}
+  '@tsparticles/shape-emoji@3.9.1':
+    resolution: {integrity: sha512-ifvY63usuT+hipgVHb8gelBHSeF6ryPnMxAAEC1RGHhhXfpSRWMtE6ybr+pSsYU52M3G9+TF84v91pSwNrb9ZQ==}
 
-  '@tsparticles/shape-image@3.9.0':
-    resolution: {integrity: sha512-Ni0fliUbovbhdevBl86Au/lelwKdwGrhZmo0hmEjNpTGB2S39X23+uQuQLmAvSmqeh8fYNnprda0RMfpQ5nIjA==}
+  '@tsparticles/shape-image@3.9.1':
+    resolution: {integrity: sha512-fCA5eme8VF3oX8yNVUA0l2SLDKuiZObkijb0z3Ky0qj1HUEVlAuEMhhNDNB9E2iELTrWEix9z7BFMePp2CC7AA==}
 
-  '@tsparticles/shape-line@3.9.0':
-    resolution: {integrity: sha512-ZXw7SPKv7QPZ4j9NFmdHAPm7u2/YyoXU9d2hlBvqqCqOiNpn75okjcSos1AGPC2oXOy/Mkqn3YAVwx4Y8oh3kw==}
+  '@tsparticles/shape-line@3.9.1':
+    resolution: {integrity: sha512-wT8NSp0N9HURyV05f371cHKcNTNqr0/cwUu6WhBzbshkYGy1KZUP9CpRIh5FCrBpTev34mEQfOXDycgfG0KiLQ==}
 
-  '@tsparticles/shape-polygon@3.9.0':
-    resolution: {integrity: sha512-iuR4oAdFivD/EkZFY72SicSfIs+EdXr/SCffPPZtesxaH6VQkk4sWC/rMMKmtex/y6Q3wnwVKZKXfsFSxsZA1Q==}
+  '@tsparticles/shape-polygon@3.9.1':
+    resolution: {integrity: sha512-dA77PgZdoLwxnliH6XQM/zF0r4jhT01pw5y7XTeTqws++hg4rTLV9255k6R6eUqKq0FPSW1/WBsBIl7q/MmrqQ==}
 
-  '@tsparticles/shape-square@3.9.0':
-    resolution: {integrity: sha512-R46hLyC6zTuAdlmR705qW+kaKp2HLxVrOBCU/xU09WhXfp/nZ/NML+rcHN8B9hOITUkDxVv78LV8xyUcN3dkmA==}
+  '@tsparticles/shape-square@3.9.1':
+    resolution: {integrity: sha512-DKGkDnRyZrAm7T2ipqNezJahSWs6xd9O5LQLe5vjrYm1qGwrFxJiQaAdlb00UNrexz1/SA7bEoIg4XKaFa7qhQ==}
 
-  '@tsparticles/shape-star@3.9.0':
-    resolution: {integrity: sha512-dZVyfxpxSxgiPUzrMFUIPpzk31vyG3OILrzdXRMfyDHA9HvJ8ElU9JNfYYMmDNBkmBCuokErz2zuJtSKX/TIjw==}
+  '@tsparticles/shape-star@3.9.1':
+    resolution: {integrity: sha512-kdMJpi8cdeb6vGrZVSxTG0JIjCwIenggqk0EYeKAwtOGZFBgL7eHhF2F6uu1oq8cJAbXPujEoabnLsz6mW8XaA==}
 
-  '@tsparticles/shape-text@3.9.0':
-    resolution: {integrity: sha512-ev+8+QaiIbN5xKR7byQ3EBdcmIf1QtQ8d1oucrv5fpEay47NDhdyfSQVZeJAa9kpb6flrhJXVlnFhXy7tyPmAg==}
+  '@tsparticles/shape-text@3.9.1':
+    resolution: {integrity: sha512-oNsLHI0lGkIXoUw3W598iwd7dtoHCDrwpwJRGnQzgfk6T5a9dCpSD5vDeQN89lr3BUbVui4lhxq+/TyC64oAqA==}
 
-  '@tsparticles/slim@3.9.0':
-    resolution: {integrity: sha512-x6E4de8goOd4mXWWBB8YeIKB/KzjkYTYc0n7Z4DupgIKylJiqmsmQ16zokcIUQFYWTfne5jLCcQzPlTubm0IJw==}
+  '@tsparticles/slim@3.9.1':
+    resolution: {integrity: sha512-CL5cDmADU7sDjRli0So+hY61VMbdroqbArmR9Av+c1Fisa5ytr6QD7Jv62iwU2S6rvgicEe9OyRmSy5GIefwZw==}
 
-  '@tsparticles/updater-color@3.9.0':
-    resolution: {integrity: sha512-8NKajfmD+eDis+hoK9Ke5dZ1tovKq29KyYCcfv/RKZy4ay1s8/Waxa429Ov+h7Pbj+kKBkNECjMuJRvtkBv3+A==}
+  '@tsparticles/updater-color@3.9.1':
+    resolution: {integrity: sha512-XGWdscrgEMA8L5E7exsE0f8/2zHKIqnTrZymcyuFBw2DCB6BIV+5z6qaNStpxrhq3DbIxxhqqcybqeOo7+Alpg==}
 
-  '@tsparticles/updater-destroy@3.9.0':
-    resolution: {integrity: sha512-RFggH7MhJ1I3mJT5Nzd0K9d7zCNdfRPXb0AJ+SBwRwRcPgBeq6/6DmAmVJtM0wzU7m6vqeT9FwyzGZ8/eA3O+g==}
+  '@tsparticles/updater-destroy@3.9.1':
+    resolution: {integrity: sha512-MjMzEhZwCQIbxO6ZRM0eXsHVwmlXuUqwC43WCPZCpjhK3AJrMu3KR4xsJieFTWIbVNguAvbgoTB10FfJOUU5VA==}
 
-  '@tsparticles/updater-life@3.9.0':
-    resolution: {integrity: sha512-vy0lj95tP+wB43cAKVpMY3ZbDTrP3hzZKPC3YliCwC6kE+RxHONSrnTWp8wl/+KuWiIftKpHI6mq8C4TSDD/mg==}
+  '@tsparticles/updater-life@3.9.1':
+    resolution: {integrity: sha512-Oi8aF2RIwMMsjssUkCB6t3PRpENHjdZf6cX92WNfAuqXtQphr3OMAkYFJFWkvyPFK22AVy3p/cFt6KE5zXxwAA==}
 
-  '@tsparticles/updater-opacity@3.9.0':
-    resolution: {integrity: sha512-qdVNRRWp4QgjtCLNz75UcRfJwy82u9N0QGLpLCOLjtB88P/itSlGhHADF5dCPIoeqRXRPzTLUBcuXDWOBrPigQ==}
+  '@tsparticles/updater-opacity@3.9.1':
+    resolution: {integrity: sha512-w778LQuRZJ+IoWzeRdrGykPYSSaTeWfBvLZ2XwYEkh/Ss961InOxZKIpcS6i5Kp/Zfw0fS1ZAuqeHwuj///Osw==}
 
-  '@tsparticles/updater-out-modes@3.9.0':
-    resolution: {integrity: sha512-WcNDJctR1OwbpDtbwYbUZdGUuuUeuFaYB1c04UgrlM33rzshpjnPQqVg+BsNS5OS7KJtYety6nZ7I8L1axHlbw==}
+  '@tsparticles/updater-out-modes@3.9.1':
+    resolution: {integrity: sha512-cKQEkAwbru+hhKF+GTsfbOvuBbx2DSB25CxOdhtW2wRvDBoCnngNdLw91rs+0Cex4tgEeibkebrIKFDDE6kELg==}
 
-  '@tsparticles/updater-roll@3.9.0':
-    resolution: {integrity: sha512-5vyc4EjK11v9ERaDllOY9AW5+KtQgcvgbpd1l1IFLudinvVWaOTQnZr/mVyJf6cmjJ1zzkJXd0wKLe5UChnYTQ==}
+  '@tsparticles/updater-roll@3.9.1':
+    resolution: {integrity: sha512-zl4JeM3gUBJ0uttmIsond3lrZ3f3AkItFeS0Lhj/7jiCKfUoRyyOMrcBk8R1AhW7lI+7ko1iBs3jhO0jnxz9vg==}
 
-  '@tsparticles/updater-rotate@3.9.0':
-    resolution: {integrity: sha512-sk33uzYZ/tIoCFLq76PdwEtB+TE4lXNGUV0/AGrzs45EVLWj6Ma8+0szNZ+5JAvKihwcDjH9YeDjiykn1gHubg==}
+  '@tsparticles/updater-rotate@3.9.1':
+    resolution: {integrity: sha512-9BfKaGfp28JN82MF2qs6Ae/lJr9EColMfMTHqSKljblwbpVDHte4umuwKl3VjbRt87WD9MGtla66NTUYl+WxuQ==}
 
-  '@tsparticles/updater-size@3.9.0':
-    resolution: {integrity: sha512-56ur/nFSbAOH5K9/GNR8rvKN/i0oxGDVwJjsQzFuwJkdddFE40VYlobIByuvBoMGzzN13JJxP8s41aIxzEvBDw==}
+  '@tsparticles/updater-size@3.9.1':
+    resolution: {integrity: sha512-3NSVs0O2ApNKZXfd+y/zNhTXSFeG1Pw4peI8e6z/q5+XLbmue9oiEwoPy/tQLaark3oNj3JU7Q903ZijPyXSzw==}
 
-  '@tsparticles/updater-stroke-color@3.9.0':
-    resolution: {integrity: sha512-GW8wMY3PZQc6dUMKb+lbP/WzNImqu0gliIE/wWPyrC5X6dCU0Hi85qHUNdsIBVU97IFhMuHQYIZe3LbDgRLUAA==}
+  '@tsparticles/updater-stroke-color@3.9.1':
+    resolution: {integrity: sha512-3x14+C2is9pZYTg9T2TiA/aM1YMq4wLdYaZDcHm3qO30DZu5oeQq0rm/6w+QOGKYY1Z3Htg9rlSUZkhTHn7eDA==}
 
-  '@tsparticles/updater-tilt@3.9.0':
-    resolution: {integrity: sha512-ll3QaDLR1fR+A6IzQF3hMK0qVGEkZ/KHCXtmCo6FsCNNBCXhMmKz18JUa1e/ZyHiK1tQnYPt8ZDpvoJghOVwbg==}
+  '@tsparticles/updater-tilt@3.9.1':
+    resolution: {integrity: sha512-PB2yaoyXRmSk4iIVgjtRrzOxXMK9mjeAQHIJGtT4faq46Z8cbIIEFgjTwqrUV8qOrNg/h4sm5NE/s0qsTYjp1Q==}
 
-  '@tsparticles/updater-twinkle@3.9.0':
-    resolution: {integrity: sha512-AJ3xgAQHUOvEdc/8pdrO0ix12zvC796AovL2oJa2Y9XkWi7saY2MY3lDjMiM3gKT20C+2vzQISIDRcHv1dFlYQ==}
+  '@tsparticles/updater-twinkle@3.9.1':
+    resolution: {integrity: sha512-xgTcYr6LmP44IPIBeQmEExN2Y5Nfl3ikmC08eOh5nZy/ta6ORP+JTsprrnfuv/O2DwTyoqFLkZ16hZfkdc1yOQ==}
 
-  '@tsparticles/updater-wobble@3.9.0':
-    resolution: {integrity: sha512-v6JS7Pll9NWEzRk9Dq0zajpFHyeMIMBLgY/GOtDIJo0rncyhDrQZ6xzZHIA6ZjLtbQP/QB3uO0E4A2vMJDqE1A==}
+  '@tsparticles/updater-wobble@3.9.1':
+    resolution: {integrity: sha512-c99Ogy9q4QWO+zsDXol0UnpUwZiY2UucFb8ltuDv9AlbGUeprygoub8jhgT5pEDv+GdzWOJGSgq7rfgv9cHBrg==}
 
   '@types/aws-lambda@8.10.150':
     resolution: {integrity: sha512-AX+AbjH/rH5ezX1fbK8onC/a+HyQHo7QGmvoxAE42n22OsciAxvZoZNEr22tbXs8WfP1nIsBjKDpgPm3HjOZbA==}
@@ -2382,8 +2382,8 @@ packages:
   '@types/node@22.17.0':
     resolution: {integrity: sha512-bbAKTCqX5aNVryi7qXVMi+OkB3w/OyblodicMbvE38blyAz7GxXf6XYhklokijuPwwVg9sDLKRxt0ZHXQwZVfQ==}
 
-  '@types/node@24.1.0':
-    resolution: {integrity: sha512-ut5FthK5moxFKH2T1CUOC6ctR67rQRvvHdFLCD2Ql6KXmMuCrjsSsRI9UsLCm9M18BMwClv4pn327UvB7eeO1w==}
+  '@types/node@24.2.0':
+    resolution: {integrity: sha512-3xyG3pMCq3oYCNg7/ZP+E1ooTaGB4cG8JWRsqqOYQdbWNY4zbaV0Ennrd7stjiJEFZCaybcIgpTjJWHRfBSIDw==}
 
   '@types/react-dom@19.1.7':
     resolution: {integrity: sha512-i5ZzwYpqjmrKenzkoLM2Ibzt6mAsM7pxB6BCIouEVVmgiqaMj1TjaK7hnA36hbW5aZv20kx7Lw6hWzPWg0Rurw==}
@@ -2412,63 +2412,63 @@ packages:
   '@types/use-sync-external-store@0.0.6':
     resolution: {integrity: sha512-zFDAD+tlpf2r4asuHEj0XH6pY6i0g5NeAHPn+15wk3BV6JA69eERFXC1gyGThDkVa1zCyKr5jox1+2LbV/AMLg==}
 
-  '@typescript-eslint/eslint-plugin@8.38.0':
-    resolution: {integrity: sha512-CPoznzpuAnIOl4nhj4tRr4gIPj5AfKgkiJmGQDaq+fQnRJTYlcBjbX3wbciGmpoPf8DREufuPRe1tNMZnGdanA==}
+  '@typescript-eslint/eslint-plugin@8.39.0':
+    resolution: {integrity: sha512-bhEz6OZeUR+O/6yx9Jk6ohX6H9JSFTaiY0v9/PuKT3oGK0rn0jNplLmyFUGV+a9gfYnVNwGDwS/UkLIuXNb2Rw==}
     engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
     peerDependencies:
-      '@typescript-eslint/parser': ^8.38.0
+      '@typescript-eslint/parser': ^8.39.0
       eslint: ^8.57.0 || ^9.0.0
-      typescript: '>=4.8.4 <5.9.0'
+      typescript: '>=4.8.4 <6.0.0'
 
-  '@typescript-eslint/parser@8.38.0':
-    resolution: {integrity: sha512-Zhy8HCvBUEfBECzIl1PKqF4p11+d0aUJS1GeUiuqK9WmOug8YCmC4h4bjyBvMyAMI9sbRczmrYL5lKg/YMbrcQ==}
+  '@typescript-eslint/parser@8.39.0':
+    resolution: {integrity: sha512-g3WpVQHngx0aLXn6kfIYCZxM6rRJlWzEkVpqEFLT3SgEDsp9cpCbxxgwnE504q4H+ruSDh/VGS6nqZIDynP+vg==}
     engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
     peerDependencies:
       eslint: ^8.57.0 || ^9.0.0
-      typescript: '>=4.8.4 <5.9.0'
+      typescript: '>=4.8.4 <6.0.0'
 
-  '@typescript-eslint/project-service@8.38.0':
-    resolution: {integrity: sha512-dbK7Jvqcb8c9QfH01YB6pORpqX1mn5gDZc9n63Ak/+jD67oWXn3Gs0M6vddAN+eDXBCS5EmNWzbSxsn9SzFWWg==}
+  '@typescript-eslint/project-service@8.39.0':
+    resolution: {integrity: sha512-CTzJqaSq30V/Z2Og9jogzZt8lJRR5TKlAdXmWgdu4hgcC9Kww5flQ+xFvMxIBWVNdxJO7OifgdOK4PokMIWPew==}
     engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
     peerDependencies:
-      typescript: '>=4.8.4 <5.9.0'
+      typescript: '>=4.8.4 <6.0.0'
 
-  '@typescript-eslint/scope-manager@8.38.0':
-    resolution: {integrity: sha512-WJw3AVlFFcdT9Ri1xs/lg8LwDqgekWXWhH3iAF+1ZM+QPd7oxQ6jvtW/JPwzAScxitILUIFs0/AnQ/UWHzbATQ==}
+  '@typescript-eslint/scope-manager@8.39.0':
+    resolution: {integrity: sha512-8QOzff9UKxOh6npZQ/4FQu4mjdOCGSdO3p44ww0hk8Vu+IGbg0tB/H1LcTARRDzGCC8pDGbh2rissBuuoPgH8A==}
     engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
 
-  '@typescript-eslint/tsconfig-utils@8.38.0':
-    resolution: {integrity: sha512-Lum9RtSE3EroKk/bYns+sPOodqb2Fv50XOl/gMviMKNvanETUuUcC9ObRbzrJ4VSd2JalPqgSAavwrPiPvnAiQ==}
+  '@typescript-eslint/tsconfig-utils@8.39.0':
+    resolution: {integrity: sha512-Fd3/QjmFV2sKmvv3Mrj8r6N8CryYiCS8Wdb/6/rgOXAWGcFuc+VkQuG28uk/4kVNVZBQuuDHEDUpo/pQ32zsIQ==}
     engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
     peerDependencies:
-      typescript: '>=4.8.4 <5.9.0'
+      typescript: '>=4.8.4 <6.0.0'
 
-  '@typescript-eslint/type-utils@8.38.0':
-    resolution: {integrity: sha512-c7jAvGEZVf0ao2z+nnz8BUaHZD09Agbh+DY7qvBQqLiz8uJzRgVPj5YvOh8I8uEiH8oIUGIfHzMwUcGVco/SJg==}
+  '@typescript-eslint/type-utils@8.39.0':
+    resolution: {integrity: sha512-6B3z0c1DXVT2vYA9+z9axjtc09rqKUPRmijD5m9iv8iQpHBRYRMBcgxSiKTZKm6FwWw1/cI4v6em35OsKCiN5Q==}
     engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
     peerDependencies:
       eslint: ^8.57.0 || ^9.0.0
-      typescript: '>=4.8.4 <5.9.0'
+      typescript: '>=4.8.4 <6.0.0'
 
-  '@typescript-eslint/types@8.38.0':
-    resolution: {integrity: sha512-wzkUfX3plUqij4YwWaJyqhiPE5UCRVlFpKn1oCRn2O1bJ592XxWJj8ROQ3JD5MYXLORW84063z3tZTb/cs4Tyw==}
+  '@typescript-eslint/types@8.39.0':
+    resolution: {integrity: sha512-ArDdaOllnCj3yn/lzKn9s0pBQYmmyme/v1HbGIGB0GB/knFI3fWMHloC+oYTJW46tVbYnGKTMDK4ah1sC2v0Kg==}
     engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
 
-  '@typescript-eslint/typescript-estree@8.38.0':
-    resolution: {integrity: sha512-fooELKcAKzxux6fA6pxOflpNS0jc+nOQEEOipXFNjSlBS6fqrJOVY/whSn70SScHrcJ2LDsxWrneFoWYSVfqhQ==}
+  '@typescript-eslint/typescript-estree@8.39.0':
+    resolution: {integrity: sha512-ndWdiflRMvfIgQRpckQQLiB5qAKQ7w++V4LlCHwp62eym1HLB/kw7D9f2e8ytONls/jt89TEasgvb+VwnRprsw==}
     engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
     peerDependencies:
-      typescript: '>=4.8.4 <5.9.0'
+      typescript: '>=4.8.4 <6.0.0'
 
-  '@typescript-eslint/utils@8.38.0':
-    resolution: {integrity: sha512-hHcMA86Hgt+ijJlrD8fX0j1j8w4C92zue/8LOPAFioIno+W0+L7KqE8QZKCcPGc/92Vs9x36w/4MPTJhqXdyvg==}
+  '@typescript-eslint/utils@8.39.0':
+    resolution: {integrity: sha512-4GVSvNA0Vx1Ktwvf4sFE+exxJ3QGUorQG1/A5mRfRNZtkBT2xrA/BCO2H0eALx/PnvCS6/vmYwRdDA41EoffkQ==}
     engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
     peerDependencies:
       eslint: ^8.57.0 || ^9.0.0
-      typescript: '>=4.8.4 <5.9.0'
+      typescript: '>=4.8.4 <6.0.0'
 
-  '@typescript-eslint/visitor-keys@8.38.0':
-    resolution: {integrity: sha512-pWrTcoFNWuwHlA9CvlfSsGWs14JxfN1TH25zM5L7o0pRLhsoZkDnTsXfQRJBEWJoV5DL0jf+Z+sxiud+K0mq1g==}
+  '@typescript-eslint/visitor-keys@8.39.0':
+    resolution: {integrity: sha512-ldgiJ+VAhQCfIjeOgu8Kj5nSxds0ktPOSO9p4+0VDH2R2pLvQraaM5Oen2d7NxzMCm+Sn/vJT+mv2H5u6b/3fA==}
     engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
 
   '@ungap/structured-clone@1.3.0':
@@ -2528,12 +2528,16 @@ packages:
     engines: {node: '>=0.4.0'}
     hasBin: true
 
+  adler-32@1.3.1:
+    resolution: {integrity: sha512-ynZ4w/nUUv5rrsR8UUGoe1VC9hZj6V5hU9Qw1HlMDJGEJw5S7TfTErWTjMys6M7vr0YWcPqs3qAr4ss0nDfP+A==}
+    engines: {node: '>=0.8'}
+
   agent-base@7.1.3:
     resolution: {integrity: sha512-jRR5wdylq8CkOe6hei19GGZnxM6rBGwFl3Bg0YItGDimvjGtAvdZk4Pu6Cl4u4Igsws4a1fd1Vq3ezrhn4KmFw==}
     engines: {node: '>= 14'}
 
-  ai@5.0.0:
-    resolution: {integrity: sha512-F4jOhOSeiZD8lXpF4l1hRqyM1jbqoLKGVZNxAP467wmQCsWUtElMa3Ki5PrDMq6qvUNC3deUKfERDAsfj7IDlg==}
+  ai@5.0.5:
+    resolution: {integrity: sha512-NPQ8Yv4lR7o/1rM+HQ0JT18zKxVFy/DrFDpvxlBQqmemSwsf3FlLNTK0asWXo9YtDAc0BOFL/y4kB56Hffi4yg==}
     engines: {node: '>=18'}
     peerDependencies:
       zod: ^3.25.76 || ^4
@@ -2648,6 +2652,10 @@ packages:
   ccount@2.0.1:
     resolution: {integrity: sha512-eyrF0jiFpY+3drT6383f1qhkbGsLSifNAjA61IUjZjmLCWjItY6LB9ft9YhoDgwfmclB2zhu51Lc7+95b8NRAg==}
 
+  cfb@1.2.2:
+    resolution: {integrity: sha512-KfdUZsSOw19/ObEWasvBP/Ac4reZvAGauZhs6S/gqNhXhI7cKwvlH7ulj+dOEYnca4bm4SGo8C1bTAQvnTjgQA==}
+    engines: {node: '>=0.8'}
+
   chalk@2.4.2:
     resolution: {integrity: sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ==}
     engines: {node: '>=4'}
@@ -2710,6 +2718,10 @@ packages:
       react: ^18 || ^19 || ^19.0.0-rc
       react-dom: ^18 || ^19 || ^19.0.0-rc
 
+  codepage@1.15.0:
+    resolution: {integrity: sha512-3g6NUTPd/YtuuGrhMnOMRjFc+LJw/bnMp3+0r/Wcz3IXUuCosKRJvMphm5+Q+bvTVGcJJuRvVLuYba+WojaFaA==}
+    engines: {node: '>=0.8'}
+
   color-convert@1.9.3:
     resolution: {integrity: sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg==}
 
@@ -2824,6 +2836,11 @@ packages:
   cose-base@2.2.0:
     resolution: {integrity: sha512-AzlgcsCbUMymkADOJtQm3wO9S3ltPfYOFD5033keQn9NJzIbtnZj+UdBJe7DYml/8TdbtHJW3j58SOnKhWY/5g==}
 
+  crc-32@1.2.2:
+    resolution: {integrity: sha512-ROmzCKrTnOwybPcJApAA6WBWij23HVfGVNKqqrZpuyZOHqK2CwHSvpGuyt/UNNvaIjEd8X5IFGp4Mh+Ie1IHJQ==}
+    engines: {node: '>=0.8'}
+    hasBin: true
+
   cross-spawn@6.0.6:
     resolution: {integrity: sha512-VqCUuhcd1iB+dsv8gxPttb5iZh/D0iubSP21g36KXdEuf6I5JiioesUVjpCdHV9MZRUfVFlvwtIUyPfxo5trtw==}
     engines: {node: '>=4.8'}
@@ -3385,6 +3402,10 @@ packages:
     resolution: {integrity: sha512-buRG0fpBtRHSTCOASe6hD258tEubFoRLb4ZNA6NxMVHNw2gOcwHo9wyablzMzOA5z9xA9L1KNjk/Nt6MT9aYow==}
     engines: {node: '>= 0.6'}
 
+  frac@1.1.2:
+    resolution: {integrity: sha512-w/XBfkibaTl3YDqASwfDUqkna4Z2p9cFSr1aHDt0WoMTECnRfBOv2WArlZILlqgWlmdIlALXGpM2AOhEk5W3IA==}
+    engines: {node: '>=0.8'}
+
   fraction.js@4.3.7:
     resolution: {integrity: sha512-ZsDfxO51wGAXREY55a7la9LScWpwv9RxIrYABrlvOFBlH/ShPnrtsXeuUIfXKKOVicNxQ+o8JTbJvjS4M89yew==}
 
@@ -4646,8 +4667,8 @@ packages:
     resolution: {integrity: sha512-BLq/cCO9two+lBgiTYNqD6GdtK8s4NpaWrl6/rCO9w0TUS8oJl7cmToOZfRYllKTISY6nt1U7jQ53brmKqY6BA==}
     engines: {node: '>=4'}
 
-  recharts@3.1.0:
-    resolution: {integrity: sha512-NqAqQcGBmLrfDs2mHX/bz8jJCQtG2FeXfE0GqpZmIuXIjkpIwj8sd9ad0WyvKiBKPd8ZgNG0hL85c8sFDwascw==}
+  recharts@3.1.2:
+    resolution: {integrity: sha512-vhNbYwaxNbk/IATK0Ki29k3qvTkGqwvCgyQAQ9MavvvBwjvKnMTswdbklJpcOAoMPN/qxF3Lyqob0zO+ZXkZ4g==}
     engines: {node: '>=18'}
     peerDependencies:
       react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
@@ -4886,8 +4907,8 @@ packages:
     resolution: {integrity: sha512-kUMbT1oBJCpgrnKoSr0o6wPtvRWT9W9UKvGLwfJYO2WuahZRHOpEyL1ckyMGgMWh0UdpmaoFqKKD29WTomNEGA==}
     engines: {node: '>=8'}
 
-  sonner@2.0.6:
-    resolution: {integrity: sha512-yHFhk8T/DK3YxjFQXIrcHT1rGEeTLliVzWbO0xN8GberVun2RiBnxAjXAYpZrqwEVHBG9asI/Li8TAAhN9m59Q==}
+  sonner@2.0.7:
+    resolution: {integrity: sha512-W6ZN4p58k8aDKA4XPcx2hpIQXBRAgyiWVkYhT7CvK6D3iAu7xjvVyhQHg2/iaKJZ1XVJ4r7XuwGL+WGEK37i9w==}
     peerDependencies:
       react: ^18.0.0 || ^19.0.0 || ^19.0.0-rc
       react-dom: ^18.0.0 || ^19.0.0 || ^19.0.0-rc
@@ -4914,6 +4935,10 @@ packages:
   spdx-license-ids@3.0.21:
     resolution: {integrity: sha512-Bvg/8F5XephndSK3JffaRqdT+gyhfqIPwDHpX80tJrF8QQRYMo8sNMeaZ2Dp5+jhwKnUmIOyFFQfHRkjJm5nXg==}
 
+  ssf@0.11.2:
+    resolution: {integrity: sha512-+idbmIXoYET47hH+d7dfm2epdOMUDjqcB4648sTZ+t2JwoyBFL/insLfB/racrDmsKB3diwsDA696pZMieAC5g==}
+    engines: {node: '>=0.8'}
+
   statuses@2.0.1:
     resolution: {integrity: sha512-RwNA9Z/7PrK06rYLIzFMlaF+l73iwpzsqRIFgbMLbTcLD6cOao82TaWefPXQvB2fOC4AjuYSEndS7N/mTCbkdQ==}
     engines: {node: '>= 0.8'}
@@ -5059,8 +5084,8 @@ packages:
     resolution: {integrity: sha512-ZjDIYex6jBJ4iMc9+z0uPe7SgBnmb6l+EJm83MPIsOny9lPpetMsnw/8YJ3xdxn8hV+S3myTpTN1CkOVmFv0QQ==}
     deprecated: starting from tsparticles v3 the packages are now moved to @tsparticles/package-name instead of tsparticles-package-name
 
-  tsparticles@3.9.0:
-    resolution: {integrity: sha512-SDnGlebScqZEIhGXhCRSFLw1MsxTxgPo1HLuLyZ+GLVy7e8lMMLxP1N2LBpOTs9MZQDYBdXoC4x1gWISK3CXPg==}
+  tsparticles@3.9.1:
+    resolution: {integrity: sha512-Y780IGSL4qjkZj7+fI92PV/cziHqLR/s6nnYri4K6vH3NQRmDK5D6pfskDO8T4Y96ChCWHY3uxPtOb/hKQ83Qg==}
 
   type-check@0.4.0:
     resolution: {integrity: sha512-XleUoc9uwGXqjWwXaUTZAmzMcFZ5858QA2vvx1Ur5xIcixXIP+8LnFDgRplU30us6teqdlskFfu+ae4K79Ooew==}
@@ -5094,12 +5119,12 @@ packages:
     resolution: {integrity: sha512-EGjWssW7Tsk4DGfE+5yluuljS1OGYWiI1J6e8puZz9nTMM51Oug8CD5Zo4gWMsOhq5BI+1bF+rWTm4Vbj3ivRA==}
     engines: {node: '>= 18'}
 
-  typescript-eslint@8.38.0:
-    resolution: {integrity: sha512-FsZlrYK6bPDGoLeZRuvx2v6qrM03I0U0SnfCLPs/XCCPCFD80xU9Pg09H/K+XFa68uJuZo7l/Xhs+eDRg2l3hg==}
+  typescript-eslint@8.39.0:
+    resolution: {integrity: sha512-lH8FvtdtzcHJCkMOKnN73LIn6SLTpoojgJqDAxPm1jCR14eWSGPX8ul/gggBdPMk/d5+u9V854vTYQ8T5jF/1Q==}
     engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
     peerDependencies:
       eslint: ^8.57.0 || ^9.0.0
-      typescript: '>=4.8.4 <5.9.0'
+      typescript: '>=4.8.4 <6.0.0'
 
   typescript@5.9.2:
     resolution: {integrity: sha512-CWBzXQrc/qOkhidw1OzBTQuYRbfyxDXJMVJ1XNwUHGROVmuaeiEm3OslpZ1RV96d7SKKjZKrSJu3+t/xlw3R9A==}
@@ -5119,8 +5144,8 @@ packages:
   undici-types@6.21.0:
     resolution: {integrity: sha512-iwDZqg0QAGrg9Rav5H4n0M64c3mkR59cJ6wQp+7C4nI0gsmExaedaYLNO44eT4AtBBwjbTiGPMlt2Md0T9H9JQ==}
 
-  undici-types@7.8.0:
-    resolution: {integrity: sha512-9UJ2xGDvQ43tYyVMpuHlsgApydB8ZKfVYTsLDhXkFL/6gfkp+U8xTGdh8pMJv1SpZna0zxG1DwsKZsreLbXBxw==}
+  undici-types@7.10.0:
+    resolution: {integrity: sha512-t5Fy/nfn+14LuOc2KNYg75vZqClpAiqscVvMygNnlsHBFpSXdJaYtXMcdNLpl/Qvc3P2cB3s6lOV51nqsFq4ag==}
 
   unicode-emoji-modifier-base@1.0.0:
     resolution: {integrity: sha512-yLSH4py7oFH3oG/9K+XWrz1pSi3dfUrWEnInbxMfArOfc1+33BlGPQtLsOYwvdMy11AwUBetYuaRxSPqgkq+8g==}
@@ -5332,10 +5357,18 @@ packages:
     engines: {node: '>= 8'}
     hasBin: true
 
+  wmf@1.0.2:
+    resolution: {integrity: sha512-/p9K7bEh0Dj6WbXg4JG0xvLQmIadrner1bi45VMJTfnbVHsc7yIajZyoSoK60/dtVBs12Fm6WkUI5/3WAVsNMw==}
+    engines: {node: '>=0.8'}
+
   word-wrap@1.2.5:
     resolution: {integrity: sha512-BN22B5eaMMI9UMtjrGd5g5eCYPpCPDUy0FJXbYsaT5zYxjFOckS53SQDE3pWkVoWpHXVb3BrYcEN4Twa55B5cA==}
     engines: {node: '>=0.10.0'}
 
+  word@0.3.0:
+    resolution: {integrity: sha512-OELeY0Q61OXpdUfTp+oweA/vtLVg5VDOXh+3he3PNzLGG/y0oylSOC1xRVj0+l4vQ3tj/bB1HVHv1ocXkQceFA==}
+    engines: {node: '>=0.8'}
+
   wrappy@1.0.2:
     resolution: {integrity: sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==}
 
@@ -5351,6 +5384,11 @@ packages:
       utf-8-validate:
         optional: true
 
+  xlsx@0.18.5:
+    resolution: {integrity: sha512-dmg3LCjBPHZnQp5/F/+nnTa+miPJxUXB6vtk42YjBBKayDNagxGEeIdWApkYPOf3Z3pm3k62Knjzp7lMeTEtFQ==}
+    engines: {node: '>=0.8'}
+    hasBin: true
+
   xtend@4.0.2:
     resolution: {integrity: sha512-LKYU1iAXJXUgAXn9URjiu+MWhyUXHsvfp7mcuYm9dSUKK0/CjtrUwFAxD82/mCWbtLsGjFIad0wIsod4zrTAEQ==}
     engines: {node: '>=0.4'}
@@ -5379,8 +5417,8 @@ packages:
   zod@3.25.67:
     resolution: {integrity: sha512-idA2YXwpCdqUSKRCACDE6ItZD9TZzy3OZMtpfLoh6oPR47lipysRrJfjzMqFxQ3uJuUPyUeWe1r9vLH33xO/Qw==}
 
-  zod@4.0.14:
-    resolution: {integrity: sha512-nGFJTnJN6cM2v9kXL+SOBq3AtjQby3Mv5ySGFof5UGRHrRioSJ5iG680cYNjE/yWk671nROcpPj4hAS8nyLhSw==}
+  zod@4.0.15:
+    resolution: {integrity: sha512-2IVHb9h4Mt6+UXkyMs0XbfICUh1eUrlJJAOupBHUhLRnKkruawyDddYRCs0Eizt900ntIMk9/4RksYl+FgSpcQ==}
 
   zwitch@1.0.5:
     resolution: {integrity: sha512-V50KMwwzqJV0NpZIZFwfOD5/lyny3WlSzRiXgA0G7VUnRlqttta1L6UQIHzd6EuBY/cHGfwTIck7w1yH6Q5zUw==}
@@ -5390,82 +5428,82 @@ packages:
 
 snapshots:
 
-  '@ai-sdk/anthropic@2.0.0(zod@4.0.14)':
+  '@ai-sdk/anthropic@2.0.1(zod@4.0.15)':
     dependencies:
       '@ai-sdk/provider': 2.0.0
-      '@ai-sdk/provider-utils': 3.0.0(zod@4.0.14)
-      zod: 4.0.14
+      '@ai-sdk/provider-utils': 3.0.1(zod@4.0.15)
+      zod: 4.0.15
 
-  '@ai-sdk/cerebras@1.0.0(zod@4.0.14)':
+  '@ai-sdk/cerebras@1.0.2(zod@4.0.15)':
     dependencies:
-      '@ai-sdk/openai-compatible': 1.0.0(zod@4.0.14)
+      '@ai-sdk/openai-compatible': 1.0.2(zod@4.0.15)
       '@ai-sdk/provider': 2.0.0
-      '@ai-sdk/provider-utils': 3.0.0(zod@4.0.14)
-      zod: 4.0.14
+      '@ai-sdk/provider-utils': 3.0.1(zod@4.0.15)
+      zod: 4.0.15
 
-  '@ai-sdk/cohere@2.0.0(zod@4.0.14)':
+  '@ai-sdk/cohere@2.0.1(zod@4.0.15)':
     dependencies:
       '@ai-sdk/provider': 2.0.0
-      '@ai-sdk/provider-utils': 3.0.0(zod@4.0.14)
-      zod: 4.0.14
+      '@ai-sdk/provider-utils': 3.0.1(zod@4.0.15)
+      zod: 4.0.15
 
-  '@ai-sdk/deepseek@1.0.0(zod@4.0.14)':
+  '@ai-sdk/deepseek@1.0.2(zod@4.0.15)':
     dependencies:
-      '@ai-sdk/openai-compatible': 1.0.0(zod@4.0.14)
+      '@ai-sdk/openai-compatible': 1.0.2(zod@4.0.15)
       '@ai-sdk/provider': 2.0.0
-      '@ai-sdk/provider-utils': 3.0.0(zod@4.0.14)
-      zod: 4.0.14
+      '@ai-sdk/provider-utils': 3.0.1(zod@4.0.15)
+      zod: 4.0.15
 
-  '@ai-sdk/gateway@1.0.0(zod@4.0.14)':
+  '@ai-sdk/gateway@1.0.3(zod@4.0.15)':
     dependencies:
       '@ai-sdk/provider': 2.0.0
-      '@ai-sdk/provider-utils': 3.0.0(zod@4.0.14)
-      zod: 4.0.14
+      '@ai-sdk/provider-utils': 3.0.1(zod@4.0.15)
+      zod: 4.0.15
 
-  '@ai-sdk/google@2.0.0(zod@4.0.14)':
+  '@ai-sdk/google@2.0.2(zod@4.0.15)':
     dependencies:
       '@ai-sdk/provider': 2.0.0
-      '@ai-sdk/provider-utils': 3.0.0(zod@4.0.14)
-      zod: 4.0.14
+      '@ai-sdk/provider-utils': 3.0.1(zod@4.0.15)
+      zod: 4.0.15
 
-  '@ai-sdk/groq@2.0.0(zod@4.0.14)':
+  '@ai-sdk/groq@2.0.2(zod@4.0.15)':
     dependencies:
       '@ai-sdk/provider': 2.0.0
-      '@ai-sdk/provider-utils': 3.0.0(zod@4.0.14)
-      zod: 4.0.14
+      '@ai-sdk/provider-utils': 3.0.1(zod@4.0.15)
+      zod: 4.0.15
 
-  '@ai-sdk/mistral@2.0.0(zod@4.0.14)':
+  '@ai-sdk/mistral@2.0.1(zod@4.0.15)':
     dependencies:
       '@ai-sdk/provider': 2.0.0
-      '@ai-sdk/provider-utils': 3.0.0(zod@4.0.14)
-      zod: 4.0.14
+      '@ai-sdk/provider-utils': 3.0.1(zod@4.0.15)
+      zod: 4.0.15
 
-  '@ai-sdk/openai-compatible@1.0.0(zod@4.0.14)':
+  '@ai-sdk/openai-compatible@1.0.2(zod@4.0.15)':
     dependencies:
       '@ai-sdk/provider': 2.0.0
-      '@ai-sdk/provider-utils': 3.0.0(zod@4.0.14)
-      zod: 4.0.14
+      '@ai-sdk/provider-utils': 3.0.1(zod@4.0.15)
+      zod: 4.0.15
 
-  '@ai-sdk/openai@2.0.0(zod@4.0.14)':
+  '@ai-sdk/openai@2.0.4(zod@4.0.15)':
     dependencies:
       '@ai-sdk/provider': 2.0.0
-      '@ai-sdk/provider-utils': 3.0.0(zod@4.0.14)
-      zod: 4.0.14
+      '@ai-sdk/provider-utils': 3.0.1(zod@4.0.15)
+      zod: 4.0.15
 
-  '@ai-sdk/provider-utils@2.2.8(zod@4.0.14)':
+  '@ai-sdk/provider-utils@2.2.8(zod@4.0.15)':
     dependencies:
       '@ai-sdk/provider': 1.1.3
       nanoid: 3.3.11
       secure-json-parse: 2.7.0
-      zod: 4.0.14
+      zod: 4.0.15
 
-  '@ai-sdk/provider-utils@3.0.0(zod@4.0.14)':
+  '@ai-sdk/provider-utils@3.0.1(zod@4.0.15)':
     dependencies:
       '@ai-sdk/provider': 2.0.0
       '@standard-schema/spec': 1.0.0
       eventsource-parser: 3.0.3
-      zod: 4.0.14
-      zod-to-json-schema: 3.24.5(zod@4.0.14)
+      zod: 4.0.15
+      zod-to-json-schema: 3.24.5(zod@4.0.15)
 
   '@ai-sdk/provider@1.1.3':
     dependencies:
@@ -5662,22 +5700,22 @@ snapshots:
     optionalDependencies:
       react: 19.1.1
 
-  '@convex-dev/resend@0.1.7(convex-helpers@0.1.100(@standard-schema/spec@1.0.0)(convex@1.25.4(react@19.1.1))(react@19.1.1)(typescript@5.9.2)(zod@4.0.14))(convex@1.25.4(react@19.1.1))(react@19.1.1)':
+  '@convex-dev/resend@0.1.9(convex-helpers@0.1.100(@standard-schema/spec@1.0.0)(convex@1.25.4(react@19.1.1))(react@19.1.1)(typescript@5.9.2)(zod@4.0.15))(convex@1.25.4(react@19.1.1))(react@19.1.1)':
     dependencies:
       '@convex-dev/rate-limiter': 0.2.12(convex@1.25.4(react@19.1.1))(react@19.1.1)
-      '@convex-dev/workpool': 0.2.17(convex-helpers@0.1.100(@standard-schema/spec@1.0.0)(convex@1.25.4(react@19.1.1))(react@19.1.1)(typescript@5.9.2)(zod@4.0.14))(convex@1.25.4(react@19.1.1))
+      '@convex-dev/workpool': 0.2.17(convex-helpers@0.1.100(@standard-schema/spec@1.0.0)(convex@1.25.4(react@19.1.1))(react@19.1.1)(typescript@5.9.2)(zod@4.0.15))(convex@1.25.4(react@19.1.1))
       convex: 1.25.4(react@19.1.1)
-      convex-helpers: 0.1.100(@standard-schema/spec@1.0.0)(convex@1.25.4(react@19.1.1))(react@19.1.1)(typescript@5.9.2)(zod@4.0.14)
+      convex-helpers: 0.1.100(@standard-schema/spec@1.0.0)(convex@1.25.4(react@19.1.1))(react@19.1.1)(typescript@5.9.2)(zod@4.0.15)
       remeda: 2.28.0
       svix: 1.70.0
     transitivePeerDependencies:
       - encoding
       - react
 
-  '@convex-dev/workpool@0.2.17(convex-helpers@0.1.100(@standard-schema/spec@1.0.0)(convex@1.25.4(react@19.1.1))(react@19.1.1)(typescript@5.9.2)(zod@4.0.14))(convex@1.25.4(react@19.1.1))':
+  '@convex-dev/workpool@0.2.17(convex-helpers@0.1.100(@standard-schema/spec@1.0.0)(convex@1.25.4(react@19.1.1))(react@19.1.1)(typescript@5.9.2)(zod@4.0.15))(convex@1.25.4(react@19.1.1))':
     dependencies:
       convex: 1.25.4(react@19.1.1)
-      convex-helpers: 0.1.100(@standard-schema/spec@1.0.0)(convex@1.25.4(react@19.1.1))(react@19.1.1)(typescript@5.9.2)(zod@4.0.14)
+      convex-helpers: 0.1.100(@standard-schema/spec@1.0.0)(convex@1.25.4(react@19.1.1))(react@19.1.1)(typescript@5.9.2)(zod@4.0.15)
 
   '@dmitryrechkin/json-schema-to-zod@1.0.1':
     dependencies:
@@ -5898,7 +5936,7 @@ snapshots:
 
   '@floating-ui/utils@0.2.9': {}
 
-  '@google/genai@1.12.0(@modelcontextprotocol/sdk@1.17.1)':
+  '@google/genai@1.13.0(@modelcontextprotocol/sdk@1.17.1)':
     dependencies:
       google-auth-library: 9.15.1
       ws: 8.18.2
@@ -6149,12 +6187,12 @@ snapshots:
       '@octokit/request-error': 7.0.0
       '@octokit/webhooks-methods': 6.0.0
 
-  '@openrouter/ai-sdk-provider@0.7.3(ai@5.0.0(zod@4.0.14))(zod@4.0.14)':
+  '@openrouter/ai-sdk-provider@0.7.3(ai@5.0.5(zod@4.0.15))(zod@4.0.15)':
     dependencies:
       '@ai-sdk/provider': 1.1.3
-      '@ai-sdk/provider-utils': 2.2.8(zod@4.0.14)
-      ai: 5.0.0(zod@4.0.14)
-      zod: 4.0.14
+      '@ai-sdk/provider-utils': 2.2.8(zod@4.0.15)
+      ai: 5.0.5(zod@4.0.15)
+      zod: 4.0.15
 
   '@opentelemetry/api@1.9.0': {}
 
@@ -6713,7 +6751,7 @@ snapshots:
     dependencies:
       react: 19.1.1
 
-  '@react-email/components@0.4.0(react-dom@19.1.1(react@19.1.1))(react@19.1.1)':
+  '@react-email/components@0.5.0(react-dom@19.1.1(react@19.1.1))(react@19.1.1)':
     dependencies:
       '@react-email/body': 0.1.0(react@19.1.1)
       '@react-email/button': 0.2.0(react@19.1.1)
@@ -6730,7 +6768,7 @@ snapshots:
       '@react-email/link': 0.0.12(react@19.1.1)
       '@react-email/markdown': 0.0.15(react@19.1.1)
       '@react-email/preview': 0.0.13(react@19.1.1)
-      '@react-email/render': 1.1.4(react-dom@19.1.1(react@19.1.1))(react@19.1.1)
+      '@react-email/render': 1.2.0(react-dom@19.1.1(react@19.1.1))(react@19.1.1)
       '@react-email/row': 0.0.12(react@19.1.1)
       '@react-email/section': 0.0.16(react@19.1.1)
       '@react-email/tailwind': 1.2.2(react@19.1.1)
@@ -6780,7 +6818,7 @@ snapshots:
     dependencies:
       react: 19.1.1
 
-  '@react-email/render@1.1.4(react-dom@19.1.1(react@19.1.1))(react@19.1.1)':
+  '@react-email/render@1.2.0(react-dom@19.1.1(react@19.1.1))(react@19.1.1)':
     dependencies:
       html-to-text: 9.0.5
       prettier: 3.6.2
@@ -7006,235 +7044,233 @@ snapshots:
       postcss-selector-parser: 6.0.10
       tailwindcss: 4.1.11
 
-  '@tsparticles/basic@3.9.0':
+  '@tsparticles/basic@3.9.1':
     dependencies:
-      '@tsparticles/engine': 3.9.0
-      '@tsparticles/move-base': 3.9.0
-      '@tsparticles/plugin-hex-color': 3.9.0
-      '@tsparticles/plugin-hsl-color': 3.9.0
-      '@tsparticles/plugin-rgb-color': 3.9.0
-      '@tsparticles/shape-circle': 3.9.0
-      '@tsparticles/updater-color': 3.9.0
-      '@tsparticles/updater-opacity': 3.9.0
-      '@tsparticles/updater-out-modes': 3.9.0
-      '@tsparticles/updater-size': 3.9.0
-
-  '@tsparticles/engine@3.8.1': {}
+      '@tsparticles/engine': 3.9.1
+      '@tsparticles/move-base': 3.9.1
+      '@tsparticles/plugin-hex-color': 3.9.1
+      '@tsparticles/plugin-hsl-color': 3.9.1
+      '@tsparticles/plugin-rgb-color': 3.9.1
+      '@tsparticles/shape-circle': 3.9.1
+      '@tsparticles/updater-color': 3.9.1
+      '@tsparticles/updater-opacity': 3.9.1
+      '@tsparticles/updater-out-modes': 3.9.1
+      '@tsparticles/updater-size': 3.9.1
 
-  '@tsparticles/engine@3.9.0': {}
+  '@tsparticles/engine@3.9.1': {}
 
-  '@tsparticles/interaction-external-attract@3.9.0':
+  '@tsparticles/interaction-external-attract@3.9.1':
     dependencies:
-      '@tsparticles/engine': 3.9.0
+      '@tsparticles/engine': 3.9.1
 
-  '@tsparticles/interaction-external-bounce@3.9.0':
+  '@tsparticles/interaction-external-bounce@3.9.1':
     dependencies:
-      '@tsparticles/engine': 3.9.0
+      '@tsparticles/engine': 3.9.1
 
-  '@tsparticles/interaction-external-bubble@3.9.0':
+  '@tsparticles/interaction-external-bubble@3.9.1':
     dependencies:
-      '@tsparticles/engine': 3.9.0
+      '@tsparticles/engine': 3.9.1
 
-  '@tsparticles/interaction-external-connect@3.9.0':
+  '@tsparticles/interaction-external-connect@3.9.1':
     dependencies:
-      '@tsparticles/engine': 3.9.0
+      '@tsparticles/engine': 3.9.1
 
-  '@tsparticles/interaction-external-grab@3.9.0':
+  '@tsparticles/interaction-external-grab@3.9.1':
     dependencies:
-      '@tsparticles/engine': 3.9.0
+      '@tsparticles/engine': 3.9.1
 
-  '@tsparticles/interaction-external-pause@3.9.0':
+  '@tsparticles/interaction-external-pause@3.9.1':
     dependencies:
-      '@tsparticles/engine': 3.9.0
+      '@tsparticles/engine': 3.9.1
 
-  '@tsparticles/interaction-external-push@3.9.0':
+  '@tsparticles/interaction-external-push@3.9.1':
     dependencies:
-      '@tsparticles/engine': 3.9.0
+      '@tsparticles/engine': 3.9.1
 
-  '@tsparticles/interaction-external-remove@3.9.0':
+  '@tsparticles/interaction-external-remove@3.9.1':
     dependencies:
-      '@tsparticles/engine': 3.9.0
+      '@tsparticles/engine': 3.9.1
 
-  '@tsparticles/interaction-external-repulse@3.9.0':
+  '@tsparticles/interaction-external-repulse@3.9.1':
     dependencies:
-      '@tsparticles/engine': 3.9.0
+      '@tsparticles/engine': 3.9.1
 
-  '@tsparticles/interaction-external-slow@3.9.0':
+  '@tsparticles/interaction-external-slow@3.9.1':
     dependencies:
-      '@tsparticles/engine': 3.9.0
+      '@tsparticles/engine': 3.9.1
 
-  '@tsparticles/interaction-external-trail@3.8.1':
+  '@tsparticles/interaction-external-trail@3.9.1':
     dependencies:
-      '@tsparticles/engine': 3.8.1
+      '@tsparticles/engine': 3.9.1
 
-  '@tsparticles/interaction-particles-attract@3.9.0':
+  '@tsparticles/interaction-particles-attract@3.9.1':
     dependencies:
-      '@tsparticles/engine': 3.9.0
+      '@tsparticles/engine': 3.9.1
 
-  '@tsparticles/interaction-particles-collisions@3.9.0':
+  '@tsparticles/interaction-particles-collisions@3.9.1':
     dependencies:
-      '@tsparticles/engine': 3.9.0
+      '@tsparticles/engine': 3.9.1
 
-  '@tsparticles/interaction-particles-links@3.9.0':
+  '@tsparticles/interaction-particles-links@3.9.1':
     dependencies:
-      '@tsparticles/engine': 3.9.0
+      '@tsparticles/engine': 3.9.1
 
-  '@tsparticles/move-base@3.9.0':
+  '@tsparticles/move-base@3.9.1':
     dependencies:
-      '@tsparticles/engine': 3.9.0
+      '@tsparticles/engine': 3.9.1
 
-  '@tsparticles/move-parallax@3.9.0':
+  '@tsparticles/move-parallax@3.9.1':
     dependencies:
-      '@tsparticles/engine': 3.9.0
+      '@tsparticles/engine': 3.9.1
 
-  '@tsparticles/plugin-absorbers@3.9.0':
+  '@tsparticles/plugin-absorbers@3.9.1':
     dependencies:
-      '@tsparticles/engine': 3.9.0
+      '@tsparticles/engine': 3.9.1
 
-  '@tsparticles/plugin-easing-quad@3.9.0':
+  '@tsparticles/plugin-easing-quad@3.9.1':
     dependencies:
-      '@tsparticles/engine': 3.9.0
+      '@tsparticles/engine': 3.9.1
 
-  '@tsparticles/plugin-emitters-shape-circle@3.9.0':
+  '@tsparticles/plugin-emitters-shape-circle@3.9.1':
     dependencies:
-      '@tsparticles/engine': 3.9.0
-      '@tsparticles/plugin-emitters': 3.9.0
+      '@tsparticles/engine': 3.9.1
+      '@tsparticles/plugin-emitters': 3.9.1
 
-  '@tsparticles/plugin-emitters-shape-square@3.9.0':
+  '@tsparticles/plugin-emitters-shape-square@3.9.1':
     dependencies:
-      '@tsparticles/engine': 3.9.0
-      '@tsparticles/plugin-emitters': 3.9.0
+      '@tsparticles/engine': 3.9.1
+      '@tsparticles/plugin-emitters': 3.9.1
 
-  '@tsparticles/plugin-emitters@3.9.0':
+  '@tsparticles/plugin-emitters@3.9.1':
     dependencies:
-      '@tsparticles/engine': 3.9.0
+      '@tsparticles/engine': 3.9.1
 
-  '@tsparticles/plugin-hex-color@3.9.0':
+  '@tsparticles/plugin-hex-color@3.9.1':
     dependencies:
-      '@tsparticles/engine': 3.9.0
+      '@tsparticles/engine': 3.9.1
 
-  '@tsparticles/plugin-hsl-color@3.9.0':
+  '@tsparticles/plugin-hsl-color@3.9.1':
     dependencies:
-      '@tsparticles/engine': 3.9.0
+      '@tsparticles/engine': 3.9.1
 
-  '@tsparticles/plugin-rgb-color@3.9.0':
+  '@tsparticles/plugin-rgb-color@3.9.1':
     dependencies:
-      '@tsparticles/engine': 3.9.0
+      '@tsparticles/engine': 3.9.1
 
-  '@tsparticles/react@3.0.0(@tsparticles/engine@3.9.0)(react-dom@19.1.1(react@19.1.1))(react@19.1.1)':
+  '@tsparticles/react@3.0.0(@tsparticles/engine@3.9.1)(react-dom@19.1.1(react@19.1.1))(react@19.1.1)':
     dependencies:
-      '@tsparticles/engine': 3.9.0
+      '@tsparticles/engine': 3.9.1
       react: 19.1.1
       react-dom: 19.1.1(react@19.1.1)
 
-  '@tsparticles/shape-circle@3.9.0':
+  '@tsparticles/shape-circle@3.9.1':
     dependencies:
-      '@tsparticles/engine': 3.9.0
+      '@tsparticles/engine': 3.9.1
 
-  '@tsparticles/shape-emoji@3.9.0':
+  '@tsparticles/shape-emoji@3.9.1':
     dependencies:
-      '@tsparticles/engine': 3.9.0
+      '@tsparticles/engine': 3.9.1
 
-  '@tsparticles/shape-image@3.9.0':
+  '@tsparticles/shape-image@3.9.1':
     dependencies:
-      '@tsparticles/engine': 3.9.0
+      '@tsparticles/engine': 3.9.1
 
-  '@tsparticles/shape-line@3.9.0':
+  '@tsparticles/shape-line@3.9.1':
     dependencies:
-      '@tsparticles/engine': 3.9.0
+      '@tsparticles/engine': 3.9.1
 
-  '@tsparticles/shape-polygon@3.9.0':
+  '@tsparticles/shape-polygon@3.9.1':
     dependencies:
-      '@tsparticles/engine': 3.9.0
+      '@tsparticles/engine': 3.9.1
 
-  '@tsparticles/shape-square@3.9.0':
+  '@tsparticles/shape-square@3.9.1':
     dependencies:
-      '@tsparticles/engine': 3.9.0
+      '@tsparticles/engine': 3.9.1
 
-  '@tsparticles/shape-star@3.9.0':
+  '@tsparticles/shape-star@3.9.1':
     dependencies:
-      '@tsparticles/engine': 3.9.0
+      '@tsparticles/engine': 3.9.1
 
-  '@tsparticles/shape-text@3.9.0':
+  '@tsparticles/shape-text@3.9.1':
     dependencies:
-      '@tsparticles/engine': 3.9.0
+      '@tsparticles/engine': 3.9.1
 
-  '@tsparticles/slim@3.9.0':
+  '@tsparticles/slim@3.9.1':
     dependencies:
-      '@tsparticles/basic': 3.9.0
-      '@tsparticles/engine': 3.9.0
-      '@tsparticles/interaction-external-attract': 3.9.0
-      '@tsparticles/interaction-external-bounce': 3.9.0
-      '@tsparticles/interaction-external-bubble': 3.9.0
-      '@tsparticles/interaction-external-connect': 3.9.0
-      '@tsparticles/interaction-external-grab': 3.9.0
-      '@tsparticles/interaction-external-pause': 3.9.0
-      '@tsparticles/interaction-external-push': 3.9.0
-      '@tsparticles/interaction-external-remove': 3.9.0
-      '@tsparticles/interaction-external-repulse': 3.9.0
-      '@tsparticles/interaction-external-slow': 3.9.0
-      '@tsparticles/interaction-particles-attract': 3.9.0
-      '@tsparticles/interaction-particles-collisions': 3.9.0
-      '@tsparticles/interaction-particles-links': 3.9.0
-      '@tsparticles/move-parallax': 3.9.0
-      '@tsparticles/plugin-easing-quad': 3.9.0
-      '@tsparticles/shape-emoji': 3.9.0
-      '@tsparticles/shape-image': 3.9.0
-      '@tsparticles/shape-line': 3.9.0
-      '@tsparticles/shape-polygon': 3.9.0
-      '@tsparticles/shape-square': 3.9.0
-      '@tsparticles/shape-star': 3.9.0
-      '@tsparticles/updater-life': 3.9.0
-      '@tsparticles/updater-rotate': 3.9.0
-      '@tsparticles/updater-stroke-color': 3.9.0
+      '@tsparticles/basic': 3.9.1
+      '@tsparticles/engine': 3.9.1
+      '@tsparticles/interaction-external-attract': 3.9.1
+      '@tsparticles/interaction-external-bounce': 3.9.1
+      '@tsparticles/interaction-external-bubble': 3.9.1
+      '@tsparticles/interaction-external-connect': 3.9.1
+      '@tsparticles/interaction-external-grab': 3.9.1
+      '@tsparticles/interaction-external-pause': 3.9.1
+      '@tsparticles/interaction-external-push': 3.9.1
+      '@tsparticles/interaction-external-remove': 3.9.1
+      '@tsparticles/interaction-external-repulse': 3.9.1
+      '@tsparticles/interaction-external-slow': 3.9.1
+      '@tsparticles/interaction-particles-attract': 3.9.1
+      '@tsparticles/interaction-particles-collisions': 3.9.1
+      '@tsparticles/interaction-particles-links': 3.9.1
+      '@tsparticles/move-parallax': 3.9.1
+      '@tsparticles/plugin-easing-quad': 3.9.1
+      '@tsparticles/shape-emoji': 3.9.1
+      '@tsparticles/shape-image': 3.9.1
+      '@tsparticles/shape-line': 3.9.1
+      '@tsparticles/shape-polygon': 3.9.1
+      '@tsparticles/shape-square': 3.9.1
+      '@tsparticles/shape-star': 3.9.1
+      '@tsparticles/updater-life': 3.9.1
+      '@tsparticles/updater-rotate': 3.9.1
+      '@tsparticles/updater-stroke-color': 3.9.1
 
-  '@tsparticles/updater-color@3.9.0':
+  '@tsparticles/updater-color@3.9.1':
     dependencies:
-      '@tsparticles/engine': 3.9.0
+      '@tsparticles/engine': 3.9.1
 
-  '@tsparticles/updater-destroy@3.9.0':
+  '@tsparticles/updater-destroy@3.9.1':
     dependencies:
-      '@tsparticles/engine': 3.9.0
+      '@tsparticles/engine': 3.9.1
 
-  '@tsparticles/updater-life@3.9.0':
+  '@tsparticles/updater-life@3.9.1':
     dependencies:
-      '@tsparticles/engine': 3.9.0
+      '@tsparticles/engine': 3.9.1
 
-  '@tsparticles/updater-opacity@3.9.0':
+  '@tsparticles/updater-opacity@3.9.1':
     dependencies:
-      '@tsparticles/engine': 3.9.0
+      '@tsparticles/engine': 3.9.1
 
-  '@tsparticles/updater-out-modes@3.9.0':
+  '@tsparticles/updater-out-modes@3.9.1':
     dependencies:
-      '@tsparticles/engine': 3.9.0
+      '@tsparticles/engine': 3.9.1
 
-  '@tsparticles/updater-roll@3.9.0':
+  '@tsparticles/updater-roll@3.9.1':
     dependencies:
-      '@tsparticles/engine': 3.9.0
+      '@tsparticles/engine': 3.9.1
 
-  '@tsparticles/updater-rotate@3.9.0':
+  '@tsparticles/updater-rotate@3.9.1':
     dependencies:
-      '@tsparticles/engine': 3.9.0
+      '@tsparticles/engine': 3.9.1
 
-  '@tsparticles/updater-size@3.9.0':
+  '@tsparticles/updater-size@3.9.1':
     dependencies:
-      '@tsparticles/engine': 3.9.0
+      '@tsparticles/engine': 3.9.1
 
-  '@tsparticles/updater-stroke-color@3.9.0':
+  '@tsparticles/updater-stroke-color@3.9.1':
     dependencies:
-      '@tsparticles/engine': 3.9.0
+      '@tsparticles/engine': 3.9.1
 
-  '@tsparticles/updater-tilt@3.9.0':
+  '@tsparticles/updater-tilt@3.9.1':
     dependencies:
-      '@tsparticles/engine': 3.9.0
+      '@tsparticles/engine': 3.9.1
 
-  '@tsparticles/updater-twinkle@3.9.0':
+  '@tsparticles/updater-twinkle@3.9.1':
     dependencies:
-      '@tsparticles/engine': 3.9.0
+      '@tsparticles/engine': 3.9.1
 
-  '@tsparticles/updater-wobble@3.9.0':
+  '@tsparticles/updater-wobble@3.9.1':
     dependencies:
-      '@tsparticles/engine': 3.9.0
+      '@tsparticles/engine': 3.9.1
 
   '@types/aws-lambda@8.10.150': {}
 
@@ -7414,16 +7450,16 @@ snapshots:
 
   '@types/node-fetch@2.6.12':
     dependencies:
-      '@types/node': 24.1.0
+      '@types/node': 24.2.0
       form-data: 4.0.3
 
   '@types/node@22.17.0':
     dependencies:
       undici-types: 6.21.0
 
-  '@types/node@24.1.0':
+  '@types/node@24.2.0':
     dependencies:
-      undici-types: 7.8.0
+      undici-types: 7.10.0
 
   '@types/react-dom@19.1.7(@types/react@19.1.9)':
     dependencies:
@@ -7439,7 +7475,7 @@ snapshots:
 
   '@types/recharts@2.0.1(@types/react@19.1.9)(react-dom@19.1.1(react@19.1.1))(react-is@18.3.1)(react@19.1.1)(redux@5.0.1)':
     dependencies:
-      recharts: 3.1.0(@types/react@19.1.9)(react-dom@19.1.1(react@19.1.1))(react-is@18.3.1)(react@19.1.1)(redux@5.0.1)
+      recharts: 3.1.2(@types/react@19.1.9)(react-dom@19.1.1(react@19.1.1))(react-is@18.3.1)(react@19.1.1)(redux@5.0.1)
     transitivePeerDependencies:
       - '@types/react'
       - react
@@ -7456,14 +7492,14 @@ snapshots:
 
   '@types/use-sync-external-store@0.0.6': {}
 
-  '@typescript-eslint/eslint-plugin@8.38.0(@typescript-eslint/parser@8.38.0(eslint@9.32.0(jiti@2.4.2))(typescript@5.9.2))(eslint@9.32.0(jiti@2.4.2))(typescript@5.9.2)':
+  '@typescript-eslint/eslint-plugin@8.39.0(@typescript-eslint/parser@8.39.0(eslint@9.32.0(jiti@2.4.2))(typescript@5.9.2))(eslint@9.32.0(jiti@2.4.2))(typescript@5.9.2)':
     dependencies:
       '@eslint-community/regexpp': 4.12.1
-      '@typescript-eslint/parser': 8.38.0(eslint@9.32.0(jiti@2.4.2))(typescript@5.9.2)
-      '@typescript-eslint/scope-manager': 8.38.0
-      '@typescript-eslint/type-utils': 8.38.0(eslint@9.32.0(jiti@2.4.2))(typescript@5.9.2)
-      '@typescript-eslint/utils': 8.38.0(eslint@9.32.0(jiti@2.4.2))(typescript@5.9.2)
-      '@typescript-eslint/visitor-keys': 8.38.0
+      '@typescript-eslint/parser': 8.39.0(eslint@9.32.0(jiti@2.4.2))(typescript@5.9.2)
+      '@typescript-eslint/scope-manager': 8.39.0
+      '@typescript-eslint/type-utils': 8.39.0(eslint@9.32.0(jiti@2.4.2))(typescript@5.9.2)
+      '@typescript-eslint/utils': 8.39.0(eslint@9.32.0(jiti@2.4.2))(typescript@5.9.2)
+      '@typescript-eslint/visitor-keys': 8.39.0
       eslint: 9.32.0(jiti@2.4.2)
       graphemer: 1.4.0
       ignore: 7.0.5
@@ -7473,41 +7509,41 @@ snapshots:
     transitivePeerDependencies:
       - supports-color
 
-  '@typescript-eslint/parser@8.38.0(eslint@9.32.0(jiti@2.4.2))(typescript@5.9.2)':
+  '@typescript-eslint/parser@8.39.0(eslint@9.32.0(jiti@2.4.2))(typescript@5.9.2)':
     dependencies:
-      '@typescript-eslint/scope-manager': 8.38.0
-      '@typescript-eslint/types': 8.38.0
-      '@typescript-eslint/typescript-estree': 8.38.0(typescript@5.9.2)
-      '@typescript-eslint/visitor-keys': 8.38.0
+      '@typescript-eslint/scope-manager': 8.39.0
+      '@typescript-eslint/types': 8.39.0
+      '@typescript-eslint/typescript-estree': 8.39.0(typescript@5.9.2)
+      '@typescript-eslint/visitor-keys': 8.39.0
       debug: 4.4.1
       eslint: 9.32.0(jiti@2.4.2)
       typescript: 5.9.2
     transitivePeerDependencies:
       - supports-color
 
-  '@typescript-eslint/project-service@8.38.0(typescript@5.9.2)':
+  '@typescript-eslint/project-service@8.39.0(typescript@5.9.2)':
     dependencies:
-      '@typescript-eslint/tsconfig-utils': 8.38.0(typescript@5.9.2)
-      '@typescript-eslint/types': 8.38.0
+      '@typescript-eslint/tsconfig-utils': 8.39.0(typescript@5.9.2)
+      '@typescript-eslint/types': 8.39.0
       debug: 4.4.1
       typescript: 5.9.2
     transitivePeerDependencies:
       - supports-color
 
-  '@typescript-eslint/scope-manager@8.38.0':
+  '@typescript-eslint/scope-manager@8.39.0':
     dependencies:
-      '@typescript-eslint/types': 8.38.0
-      '@typescript-eslint/visitor-keys': 8.38.0
+      '@typescript-eslint/types': 8.39.0
+      '@typescript-eslint/visitor-keys': 8.39.0
 
-  '@typescript-eslint/tsconfig-utils@8.38.0(typescript@5.9.2)':
+  '@typescript-eslint/tsconfig-utils@8.39.0(typescript@5.9.2)':
     dependencies:
       typescript: 5.9.2
 
-  '@typescript-eslint/type-utils@8.38.0(eslint@9.32.0(jiti@2.4.2))(typescript@5.9.2)':
+  '@typescript-eslint/type-utils@8.39.0(eslint@9.32.0(jiti@2.4.2))(typescript@5.9.2)':
     dependencies:
-      '@typescript-eslint/types': 8.38.0
-      '@typescript-eslint/typescript-estree': 8.38.0(typescript@5.9.2)
-      '@typescript-eslint/utils': 8.38.0(eslint@9.32.0(jiti@2.4.2))(typescript@5.9.2)
+      '@typescript-eslint/types': 8.39.0
+      '@typescript-eslint/typescript-estree': 8.39.0(typescript@5.9.2)
+      '@typescript-eslint/utils': 8.39.0(eslint@9.32.0(jiti@2.4.2))(typescript@5.9.2)
       debug: 4.4.1
       eslint: 9.32.0(jiti@2.4.2)
       ts-api-utils: 2.1.0(typescript@5.9.2)
@@ -7515,14 +7551,14 @@ snapshots:
     transitivePeerDependencies:
       - supports-color
 
-  '@typescript-eslint/types@8.38.0': {}
+  '@typescript-eslint/types@8.39.0': {}
 
-  '@typescript-eslint/typescript-estree@8.38.0(typescript@5.9.2)':
+  '@typescript-eslint/typescript-estree@8.39.0(typescript@5.9.2)':
     dependencies:
-      '@typescript-eslint/project-service': 8.38.0(typescript@5.9.2)
-      '@typescript-eslint/tsconfig-utils': 8.38.0(typescript@5.9.2)
-      '@typescript-eslint/types': 8.38.0
-      '@typescript-eslint/visitor-keys': 8.38.0
+      '@typescript-eslint/project-service': 8.39.0(typescript@5.9.2)
+      '@typescript-eslint/tsconfig-utils': 8.39.0(typescript@5.9.2)
+      '@typescript-eslint/types': 8.39.0
+      '@typescript-eslint/visitor-keys': 8.39.0
       debug: 4.4.1
       fast-glob: 3.3.3
       is-glob: 4.0.3
@@ -7533,20 +7569,20 @@ snapshots:
     transitivePeerDependencies:
       - supports-color
 
-  '@typescript-eslint/utils@8.38.0(eslint@9.32.0(jiti@2.4.2))(typescript@5.9.2)':
+  '@typescript-eslint/utils@8.39.0(eslint@9.32.0(jiti@2.4.2))(typescript@5.9.2)':
     dependencies:
       '@eslint-community/eslint-utils': 4.7.0(eslint@9.32.0(jiti@2.4.2))
-      '@typescript-eslint/scope-manager': 8.38.0
-      '@typescript-eslint/types': 8.38.0
-      '@typescript-eslint/typescript-estree': 8.38.0(typescript@5.9.2)
+      '@typescript-eslint/scope-manager': 8.39.0
+      '@typescript-eslint/types': 8.39.0
+      '@typescript-eslint/typescript-estree': 8.39.0(typescript@5.9.2)
       eslint: 9.32.0(jiti@2.4.2)
       typescript: 5.9.2
     transitivePeerDependencies:
       - supports-color
 
-  '@typescript-eslint/visitor-keys@8.38.0':
+  '@typescript-eslint/visitor-keys@8.39.0':
     dependencies:
-      '@typescript-eslint/types': 8.38.0
+      '@typescript-eslint/types': 8.39.0
       eslint-visitor-keys: 4.2.1
 
   '@ungap/structured-clone@1.3.0': {}
@@ -7559,7 +7595,7 @@ snapshots:
     optionalDependencies:
       react: 19.1.1
 
-  '@vitejs/plugin-react@4.7.0(vite@7.0.6(@types/node@24.1.0)(jiti@2.4.2)(lightningcss@1.30.1)(yaml@2.8.0))':
+  '@vitejs/plugin-react@4.7.0(vite@7.0.6(@types/node@24.2.0)(jiti@2.4.2)(lightningcss@1.30.1)(yaml@2.8.0))':
     dependencies:
       '@babel/core': 7.28.0
       '@babel/plugin-transform-react-jsx-self': 7.27.1(@babel/core@7.28.0)
@@ -7567,7 +7603,7 @@ snapshots:
       '@rolldown/pluginutils': 1.0.0-beta.27
       '@types/babel__core': 7.20.5
       react-refresh: 0.17.0
-      vite: 7.0.6(@types/node@24.1.0)(jiti@2.4.2)(lightningcss@1.30.1)(yaml@2.8.0)
+      vite: 7.0.6(@types/node@24.2.0)(jiti@2.4.2)(lightningcss@1.30.1)(yaml@2.8.0)
     transitivePeerDependencies:
       - supports-color
 
@@ -7587,15 +7623,17 @@ snapshots:
 
   acorn@8.15.0: {}
 
+  adler-32@1.3.1: {}
+
   agent-base@7.1.3: {}
 
-  ai@5.0.0(zod@4.0.14):
+  ai@5.0.5(zod@4.0.15):
     dependencies:
-      '@ai-sdk/gateway': 1.0.0(zod@4.0.14)
+      '@ai-sdk/gateway': 1.0.3(zod@4.0.15)
       '@ai-sdk/provider': 2.0.0
-      '@ai-sdk/provider-utils': 3.0.0(zod@4.0.14)
+      '@ai-sdk/provider-utils': 3.0.1(zod@4.0.15)
       '@opentelemetry/api': 1.9.0
-      zod: 4.0.14
+      zod: 4.0.15
 
   ajv@6.12.6:
     dependencies:
@@ -7724,6 +7762,11 @@ snapshots:
 
   ccount@2.0.1: {}
 
+  cfb@1.2.2:
+    dependencies:
+      adler-32: 1.3.1
+      crc-32: 1.2.2
+
   chalk@2.4.2:
     dependencies:
       ansi-styles: 3.2.1
@@ -7787,6 +7830,8 @@ snapshots:
       - '@types/react'
       - '@types/react-dom'
 
+  codepage@1.15.0: {}
+
   color-convert@1.9.3:
     dependencies:
       color-name: 1.1.3
@@ -7827,14 +7872,14 @@ snapshots:
 
   convert-source-map@2.0.0: {}
 
-  convex-helpers@0.1.100(@standard-schema/spec@1.0.0)(convex@1.25.4(react@19.1.1))(react@19.1.1)(typescript@5.9.2)(zod@4.0.14):
+  convex-helpers@0.1.100(@standard-schema/spec@1.0.0)(convex@1.25.4(react@19.1.1))(react@19.1.1)(typescript@5.9.2)(zod@4.0.15):
     dependencies:
       convex: 1.25.4(react@19.1.1)
     optionalDependencies:
       '@standard-schema/spec': 1.0.0
       react: 19.1.1
       typescript: 5.9.2
-      zod: 4.0.14
+      zod: 4.0.15
 
   convex@1.25.4(react@19.1.1):
     dependencies:
@@ -7863,6 +7908,8 @@ snapshots:
     dependencies:
       layout-base: 2.0.1
 
+  crc-32@1.2.2: {}
+
   cross-spawn@6.0.6:
     dependencies:
       nice-try: 1.0.5
@@ -8569,6 +8616,8 @@ snapshots:
 
   forwarded@0.2.0: {}
 
+  frac@1.1.2: {}
+
   fraction.js@4.3.7: {}
 
   fraction.js@5.2.2: {}
@@ -10163,7 +10212,7 @@ snapshots:
       normalize-package-data: 2.5.0
       path-type: 3.0.0
 
-  recharts@3.1.0(@types/react@19.1.9)(react-dom@19.1.1(react@19.1.1))(react-is@18.3.1)(react@19.1.1)(redux@5.0.1):
+  recharts@3.1.2(@types/react@19.1.9)(react-dom@19.1.1(react@19.1.1))(react-is@18.3.1)(react@19.1.1)(redux@5.0.1):
     dependencies:
       '@reduxjs/toolkit': 2.8.2(react-redux@9.2.0(@types/react@19.1.9)(react@19.1.1)(redux@5.0.1))(react@19.1.1)
       clsx: 2.1.1
@@ -10554,7 +10603,7 @@ snapshots:
     dependencies:
       unicode-emoji-modifier-base: 1.0.0
 
-  sonner@2.0.6(react-dom@19.1.1(react@19.1.1))(react@19.1.1):
+  sonner@2.0.7(react-dom@19.1.1(react@19.1.1))(react@19.1.1):
     dependencies:
       react: 19.1.1
       react-dom: 19.1.1(react@19.1.1)
@@ -10579,6 +10628,10 @@ snapshots:
 
   spdx-license-ids@3.0.21: {}
 
+  ssf@0.11.2:
+    dependencies:
+      frac: 1.1.2
+
   statuses@2.0.1: {}
 
   statuses@2.0.2: {}
@@ -10627,11 +10680,11 @@ snapshots:
 
   strip-json-comments@3.1.1: {}
 
-  stripe@18.4.0(@types/node@24.1.0):
+  stripe@18.4.0(@types/node@24.2.0):
     dependencies:
       qs: 6.14.0
     optionalDependencies:
-      '@types/node': 24.1.0
+      '@types/node': 24.2.0
 
   style-to-js@1.1.16:
     dependencies:
@@ -10726,21 +10779,21 @@ snapshots:
 
   tsparticles-engine@2.12.0: {}
 
-  tsparticles@3.9.0:
-    dependencies:
-      '@tsparticles/engine': 3.9.0
-      '@tsparticles/interaction-external-trail': 3.8.1
-      '@tsparticles/plugin-absorbers': 3.9.0
-      '@tsparticles/plugin-emitters': 3.9.0
-      '@tsparticles/plugin-emitters-shape-circle': 3.9.0
-      '@tsparticles/plugin-emitters-shape-square': 3.9.0
-      '@tsparticles/shape-text': 3.9.0
-      '@tsparticles/slim': 3.9.0
-      '@tsparticles/updater-destroy': 3.9.0
-      '@tsparticles/updater-roll': 3.9.0
-      '@tsparticles/updater-tilt': 3.9.0
-      '@tsparticles/updater-twinkle': 3.9.0
-      '@tsparticles/updater-wobble': 3.9.0
+  tsparticles@3.9.1:
+    dependencies:
+      '@tsparticles/engine': 3.9.1
+      '@tsparticles/interaction-external-trail': 3.9.1
+      '@tsparticles/plugin-absorbers': 3.9.1
+      '@tsparticles/plugin-emitters': 3.9.1
+      '@tsparticles/plugin-emitters-shape-circle': 3.9.1
+      '@tsparticles/plugin-emitters-shape-square': 3.9.1
+      '@tsparticles/shape-text': 3.9.1
+      '@tsparticles/slim': 3.9.1
+      '@tsparticles/updater-destroy': 3.9.1
+      '@tsparticles/updater-roll': 3.9.1
+      '@tsparticles/updater-tilt': 3.9.1
+      '@tsparticles/updater-twinkle': 3.9.1
+      '@tsparticles/updater-wobble': 3.9.1
 
   type-check@0.4.0:
     dependencies:
@@ -10789,12 +10842,12 @@ snapshots:
 
   typed-function@4.2.1: {}
 
-  typescript-eslint@8.38.0(eslint@9.32.0(jiti@2.4.2))(typescript@5.9.2):
+  typescript-eslint@8.39.0(eslint@9.32.0(jiti@2.4.2))(typescript@5.9.2):
     dependencies:
-      '@typescript-eslint/eslint-plugin': 8.38.0(@typescript-eslint/parser@8.38.0(eslint@9.32.0(jiti@2.4.2))(typescript@5.9.2))(eslint@9.32.0(jiti@2.4.2))(typescript@5.9.2)
-      '@typescript-eslint/parser': 8.38.0(eslint@9.32.0(jiti@2.4.2))(typescript@5.9.2)
-      '@typescript-eslint/typescript-estree': 8.38.0(typescript@5.9.2)
-      '@typescript-eslint/utils': 8.38.0(eslint@9.32.0(jiti@2.4.2))(typescript@5.9.2)
+      '@typescript-eslint/eslint-plugin': 8.39.0(@typescript-eslint/parser@8.39.0(eslint@9.32.0(jiti@2.4.2))(typescript@5.9.2))(eslint@9.32.0(jiti@2.4.2))(typescript@5.9.2)
+      '@typescript-eslint/parser': 8.39.0(eslint@9.32.0(jiti@2.4.2))(typescript@5.9.2)
+      '@typescript-eslint/typescript-estree': 8.39.0(typescript@5.9.2)
+      '@typescript-eslint/utils': 8.39.0(eslint@9.32.0(jiti@2.4.2))(typescript@5.9.2)
       eslint: 9.32.0(jiti@2.4.2)
       typescript: 5.9.2
     transitivePeerDependencies:
@@ -10815,7 +10868,7 @@ snapshots:
 
   undici-types@6.21.0: {}
 
-  undici-types@7.8.0: {}
+  undici-types@7.10.0: {}
 
   unicode-emoji-modifier-base@1.0.0: {}
 
@@ -10949,7 +11002,7 @@ snapshots:
       d3-time: 3.1.0
       d3-timer: 3.0.1
 
-  vite@7.0.6(@types/node@24.1.0)(jiti@2.4.2)(lightningcss@1.30.1)(yaml@2.8.0):
+  vite@7.0.6(@types/node@24.2.0)(jiti@2.4.2)(lightningcss@1.30.1)(yaml@2.8.0):
     dependencies:
       esbuild: 0.25.5
       fdir: 6.4.6(picomatch@4.0.3)
@@ -10958,7 +11011,7 @@ snapshots:
       rollup: 4.43.0
       tinyglobby: 0.2.14
     optionalDependencies:
-      '@types/node': 24.1.0
+      '@types/node': 24.2.0
       fsevents: 2.3.3
       jiti: 2.4.2
       lightningcss: 1.30.1
@@ -11043,12 +11096,26 @@ snapshots:
     dependencies:
       isexe: 2.0.0
 
+  wmf@1.0.2: {}
+
   word-wrap@1.2.5: {}
 
+  word@0.3.0: {}
+
   wrappy@1.0.2: {}
 
   ws@8.18.2: {}
 
+  xlsx@0.18.5:
+    dependencies:
+      adler-32: 1.3.1
+      cfb: 1.2.2
+      codepage: 1.15.0
+      crc-32: 1.2.2
+      ssf: 0.11.2
+      wmf: 1.0.2
+      word: 0.3.0
+
   xtend@4.0.2: {}
 
   yallist@3.1.1: {}
@@ -11064,13 +11131,13 @@ snapshots:
     dependencies:
       zod: 3.25.67
 
-  zod-to-json-schema@3.24.5(zod@4.0.14):
+  zod-to-json-schema@3.24.5(zod@4.0.15):
     dependencies:
-      zod: 4.0.14
+      zod: 4.0.15
 
   zod@3.25.67: {}
 
-  zod@4.0.14: {}
+  zod@4.0.15: {}
 
   zwitch@1.0.5: {}
 
diff --git a/src/App.tsx b/src/App.tsx
index aaade30..80cf2d5 100644
--- a/src/App.tsx
+++ b/src/App.tsx
@@ -13,6 +13,7 @@ import { LibraryPage } from "./components/LibraryPage";
 import { ReposePage } from "./components/ReposePage";
 import { PrivacyPolicyPage } from "./components/PrivacyPolicyPage";
 import { TermsPage } from "./components/TermsPage";
+import { SidebarProvider } from "./components/ui/sidebar";
 
 function AppContent() {
   const { theme } = useTheme();
@@ -95,7 +96,9 @@ function AppContent() {
           <div className="min-h-screen flex flex-col bg-gradient-to-br from-background via-background/98 to-muted/20 relative overflow-hidden">
             <div className="absolute inset-0 bg-gradient-to-tr from-primary/2 via-transparent to-accent/2 pointer-events-none"></div>
             <div className="relative z-10 min-h-screen flex flex-col">
+              <SidebarProvider>
               <ChatInterface mainContent={<LibraryPage />} />
+            </SidebarProvider>
             </div>
           </div>
         </Authenticated>
@@ -115,7 +118,9 @@ function AppContent() {
           <div className="min-h-screen flex flex-col bg-gradient-to-br from-background via-background/98 to-muted/20 relative overflow-hidden">
             <div className="absolute inset-0 bg-gradient-to-tr from-primary/2 via-transparent to-accent/2 pointer-events-none"></div>
             <div className="relative z-10 min-h-screen flex flex-col">
+              <SidebarProvider>
               <ChatInterface mainContent={<ReposePage />} />
+            </SidebarProvider>
             </div>
           </div>
         </Authenticated>
@@ -154,7 +159,9 @@ function AppContent() {
         <div className="min-h-screen flex flex-col bg-gradient-to-br from-background via-background/98 to-muted/20 relative overflow-hidden">
           <div className="absolute inset-0 bg-gradient-to-tr from-primary/2 via-transparent to-accent/2 pointer-events-none"></div>
           <div className="relative z-10 min-h-screen flex flex-col">
+            <SidebarProvider>
             <ChatInterface />
+            </SidebarProvider>
           </div>
         </div>
       </Authenticated>
diff --git a/src/components/ChatArea.tsx b/src/components/ChatArea.tsx
index ca3d2e2..baf7579 100644
--- a/src/components/ChatArea.tsx
+++ b/src/components/ChatArea.tsx
@@ -2,7 +2,7 @@ import { useEffect, useRef, useCallback, memo, useMemo } from "react";
 import { Id } from "../../convex/_generated/dataModel";
 import { MessageBubble } from "./MessageBubble";
 import { MessageCircle, Lightbulb, Code, Plane, Sparkles, ArrowRight, Briefcase, BarChart2, GraduationCap, Film, PenTool, Heart, GitBranch, Target, Globe, Calculator, Image, Bug, FileText, Music, Dumbbell, Coffee, Palette, BookOpen, Database, Mail, TrendingUp, MapPin, Leaf, HeartPulse, UtensilsCrossed, Camera, Megaphone, PencilLine } from 'lucide-react';
-import { Button } from "@/components/ui/button";
+
 import { LoadingDots } from "@/components/ui/loading-dots";
 import { useMutation, useAction, useQuery } from "convex/react";
 import { api } from "../../convex/_generated/api";
@@ -625,8 +625,8 @@ export const ChatArea = memo(function ChatArea({ messages, isGenerating, convers
   }
 
   return (
-    <div className="h-full overflow-y-auto p-2 sm:p-6 md:p-8 bg-gradient-to-b from-background/50 to-background" style={{ paddingBottom: (bottomPadding ?? 176) + 32 }}>
-      <div className="max-w-4xl mx-auto space-y-6 sm:space-y-8">
+    <div className="h-full overflow-y-auto pt-2 sm:pt-6 md:pt-8 px-0 bg-gradient-to-b from-background/50 to-background" style={{ paddingBottom: (bottomPadding ?? 176) + 32 }}>
+      <div className="max-w-4xl mx-auto px-3 sm:px-6 space-y-6 sm:space-y-8">
         {messages.length === 0 && !isGenerating ? (
           <div className="text-center py-16 sm:py-24">
             <div className="relative">
diff --git a/src/components/ChatInterface.tsx b/src/components/ChatInterface.tsx
index 804da01..c4060ad 100644
--- a/src/components/ChatInterface.tsx
+++ b/src/components/ChatInterface.tsx
@@ -3,11 +3,12 @@ import { useQuery, useMutation, useAction } from "convex/react";
 import { api } from "../../convex/_generated/api";
 import { Id } from "../../convex/_generated/dataModel";
 import { Sidebar } from "./Sidebar";
+import { SidebarTrigger, useSidebar } from "@/components/ui/sidebar";
 import { ChatArea } from "./ChatArea";
 import { MessageInput } from "./MessageInput";
 import { getMinimalError } from "@/lib/errorUtils";
 
-import { Menu, Settings, GitBranch, ChevronDown, Trash2, Clock, Hash, MoreHorizontal, Loader2, Plus } from 'lucide-react';
+import { Settings, GitBranch, ChevronDown, Trash2, Clock, Hash, MoreHorizontal, Loader2, Plus } from 'lucide-react';
 import { SignOutButton } from "../SignOutButton";
 import { ModeToggle } from "./ModeToggle";
 import { Button } from "@/components/ui/button";
@@ -174,10 +175,10 @@ interface ChatInterfaceProps {
 
 export default function ChatInterface({ mainContent }: ChatInterfaceProps) {
   const isDesktop = useMediaQuery("(min-width: 1024px)");
+  const { open: sidebarOpen } = useSidebar();
   const [currentConversationId, setCurrentConversationId] = useState<Id<"conversations"> | null>(null);
   const [currentBranchId, setCurrentBranchId] = useState<string>("main");
-  const [sidebarOpen, setSidebarOpen] = useState(false); // Will be set correctly in useEffect
-  const [mobileSidebarOpen, setMobileSidebarOpen] = useState(false);
+
   const [voiceDialogOpen, setVoiceDialogOpen] = useState(false);
   const [loadingBranch, setLoadingBranch] = useState(false);
 
@@ -250,14 +251,7 @@ export default function ChatInterface({ mainContent }: ChatInterfaceProps) {
     }
   }, [currentBranch, loadingBranch]);
 
-  useEffect(() => {
-    if (isDesktop) {
-      setMobileSidebarOpen(false);
-    } else {
-      // On mobile, sync sidebarOpen with mobileSidebarOpen, default to closed
-      setSidebarOpen(false);
-    }
-  }, [isDesktop]);
+
 
   // Watch for completion of AI generation by monitoring the last message
   useEffect(() => {
@@ -304,10 +298,7 @@ export default function ChatInterface({ mainContent }: ChatInterfaceProps) {
     }
   }, [currentConversationId, initializeMainBranch, migrateMessagesToMainBranch, cleanupOldToolMessages]);
 
-  // Set sidebar state based on screen size
-  useEffect(() => {
-    setSidebarOpen(isDesktop);
-  }, [isDesktop]);
+
 
   const handleNewConversation = useCallback(async () => {
     try {
@@ -318,19 +309,12 @@ export default function ChatInterface({ mainContent }: ChatInterfaceProps) {
       setCurrentConversationId(newConversationId);
       setCurrentBranchId("main");
       setShouldScrollToBottom(false);
-      
-      if (!isDesktop) {
-        setMobileSidebarOpen(false);
-      }
     } catch (error) {
       console.error("Failed to create new conversation:", error);
       // Fallback to the old behavior if creation fails
       setCurrentConversationId(null);
       setCurrentBranchId("main");
       setShouldScrollToBottom(false);
-      if (!isDesktop) {
-        setMobileSidebarOpen(false);
-      }
     }
   }, [isDesktop, createConversation]);
 
@@ -836,22 +820,13 @@ Your task is to call the \`canvas\` tool with the new, updated content. You must
   const handleSelectConversation = useCallback((id: Id<"conversations">) => {
     setCurrentConversationId(id);
     setLoadingBranch(true);
-    if (!isDesktop) {
-      setMobileSidebarOpen(false);
-    }
     if (window.location.pathname === "/library") {
       window.history.pushState({}, "", "/");
       window.dispatchEvent(new PopStateEvent("popstate"));
     }
   }, [isDesktop]);
 
-  const handleToggleSidebar = useCallback(() => {
-    if (isDesktop) {
-      setSidebarOpen(prev => !prev);
-    } else {
-      setMobileSidebarOpen(prev => !prev);
-    }
-  }, [isDesktop]);
+
 
   // Memoized callback to prevent infinite loops
   const handleScrolled = useCallback(() => {
@@ -965,77 +940,35 @@ Your task is to call the \`canvas\` tool with the new, updated content. You must
 
   return (
     <div className="flex h-screen bg-background text-foreground overflow-hidden">
-      <TooltipProvider>
-        {/* Sidebar for Mobile */}
-        <div
-          className={cn(
-            "fixed inset-y-0 left-0 z-40 h-full transition-transform duration-300 ease-in-out lg:hidden",
-            mobileSidebarOpen ? "translate-x-0" : "-translate-x-full"
-          )}
-        >
-          <Sidebar
-            currentConversationId={currentConversationId}
-            onSelectConversation={handleSelectConversation}
-            onNewConversation={() => { void handleNewConversation(); }}
-            isSidebarOpen={mobileSidebarOpen}
-            onToggleSidebar={handleToggleSidebar}
-          />
-        </div>
-
-        {/* Sidebar for Desktop */}
-        <div
-          className={cn(
-            "hidden lg:flex lg:flex-shrink-0 h-full transition-all duration-300 ease-in-out",
-            sidebarOpen ? "w-80" : "w-0 overflow-hidden"
-          )}
-        >
-           <Sidebar
-            currentConversationId={currentConversationId}
-            onSelectConversation={handleSelectConversation}
-            onNewConversation={() => { void handleNewConversation(); }}
-            isSidebarOpen={sidebarOpen}
-            onToggleSidebar={handleToggleSidebar}
-          />
-        </div>
-
-        {/* Overlay for mobile */}
-        {!isDesktop && mobileSidebarOpen && (
-          <div
-            className="fixed inset-0 bg-black/60 z-30"
-            onClick={() => setMobileSidebarOpen(false)}
-          />
-        )}
+        <Sidebar
+          currentConversationId={currentConversationId}
+          onSelectConversation={handleSelectConversation}
+          onNewConversation={() => { void handleNewConversation(); }}
+        />
+        
+        <TooltipProvider>
 
         <div className="flex-1 flex flex-col min-h-0">
           <header className="border-b border-border/50 px-4 sm:px-6 py-3 flex items-center justify-between h-20 bg-background/80 backdrop-blur-sm sticky top-0 z-20">
             <div className="flex items-center gap-2 sm:gap-4">
-                <Button
-                  variant="ghost"
-                  size="icon"
-                  onClick={handleToggleSidebar}
-                  className="rounded-xl h-10 w-10 sm:h-12 sm:w-12"
-                >
-                  <Menu size={22} />
-                </Button>
-
-                {/* Show plus button when sidebar is collapsed on desktop */}
-                {isDesktop && !sidebarOpen && (
-                  <Tooltip>
-                    <TooltipTrigger asChild>
-                      <Button
-                        variant="ghost"
-                        size="icon"
-                        onClick={() => void handleNewConversation()}
-                        className="rounded-xl h-10 w-10 sm:h-12 sm:w-12"
-                      >
-                        <Plus size={22} />
-                      </Button>
-                    </TooltipTrigger>
-                    <TooltipContent>
-                      <p>New Chat</p>
-                    </TooltipContent>
-                  </Tooltip>
-                )}
+                <SidebarTrigger className="rounded-xl h-10 w-10 sm:h-12 sm:w-12" />
+
+                {/* New chat button */}
+                <Tooltip>
+                  <TooltipTrigger asChild>
+                    <Button
+                      variant="ghost"
+                      size="icon"
+                      onClick={() => void handleNewConversation()}
+                      className="rounded-xl h-10 w-10 sm:h-12 sm:w-12"
+                    >
+                      <Plus size={22} />
+                    </Button>
+                  </TooltipTrigger>
+                  <TooltipContent>
+                    <p>New Chat</p>
+                  </TooltipContent>
+                </Tooltip>
              
               {currentConversationId && branchStats && branchStats.length > 1 && (
                 <BranchManager
@@ -1145,7 +1078,7 @@ Your task is to call the \`canvas\` tool with the new, updated content. You must
           onNewConversation={() => { void handleNewConversation(); }}
         />
 
-      </TooltipProvider>
-    </div>
+        </TooltipProvider>
+      </div>
   );
 }
diff --git a/src/components/MessageInput.tsx b/src/components/MessageInput.tsx
index e2c3cf7..35eafca 100644
--- a/src/components/MessageInput.tsx
+++ b/src/components/MessageInput.tsx
@@ -577,6 +577,11 @@ export const MessageInput = memo(function MessageInput({
           "application/msword", // doc
           "application/rtf", // rtf
           "text/rtf", // rtf alternative mime
+          "application/vnd.ms-excel", // xls
+          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", // xlsx
+          "application/vnd.ms-excel.sheet.macroenabled.12", // xlsm
+          "application/vnd.openxmlformats-officedocument.spreadsheetml.template", // xltx
+          "application/vnd.ms-excel.template.macroenabled.12", // xltm
         ]
 
         // Additional supported code/text file extensions
@@ -648,6 +653,12 @@ export const MessageInput = memo(function MessageInput({
           file.name.toLowerCase().endsWith(".scss") ||
           file.name.toLowerCase().endsWith(".sass") ||
           file.name.toLowerCase().endsWith(".less") ||
+          file.name.toLowerCase().endsWith(".csv") ||
+          file.name.toLowerCase().endsWith(".xlsx") ||
+          file.name.toLowerCase().endsWith(".xlsm") ||
+          file.name.toLowerCase().endsWith(".xls") ||
+          file.name.toLowerCase().endsWith(".xltx") ||
+          file.name.toLowerCase().endsWith(".xltm") ||
           file.name.toLowerCase().endsWith(".json") ||
           file.name.toLowerCase().endsWith(".yaml") ||
           file.name.toLowerCase().endsWith(".yml") ||
@@ -1134,7 +1145,7 @@ export const MessageInput = memo(function MessageInput({
                 e.target.value = '' // Reset input to allow selecting the same file again
               }
             }}
-            accept=".png,.jpg,.jpeg,.gif,.webp,.pdf,.txt,.md,.markdown,.docx,.doc,.rtf,.js,.ts,.jsx,.tsx,.py,.java,.c,.cpp,.cc,.cxx,.h,.hpp,.php,.rb,.go,.rs,.swift,.kt,.scala,.sh,.bash,.zsh,.fish,.html,.htm,.xml,.css,.scss,.sass,.less,.json,.yaml,.yml,.toml,.ini,.cfg,.conf"
+            accept=".png,.jpg,.jpeg,.gif,.webp,.pdf,.txt,.md,.markdown,.docx,.doc,.rtf,.csv,.xls,.xlsx,.xlsm,.js,.ts,.jsx,.tsx,.py,.java,.c,.cpp,.cc,.cxx,.h,.hpp,.php,.rb,.go,.rs,.swift,.kt,.scala,.sh,.bash,.zsh,.fish,.html,.htm,.xml,.css,.scss,.sass,.less,.json,.yaml,.yml,.toml,.ini,.cfg,.conf"
             className="hidden"
           />
         </div>
diff --git a/src/components/OptionsSelector.tsx b/src/components/OptionsSelector.tsx
index 114f3e7..05da15b 100644
--- a/src/components/OptionsSelector.tsx
+++ b/src/components/OptionsSelector.tsx
@@ -254,7 +254,7 @@ export function OptionsSelector({
               className={cn(
                 "h-9 w-9 rounded-lg",
                 activeCount > 0 ? "text-foreground" : "text-muted-foreground",
-                "hover:bg-muted/50 hover:text-foreground focus-visible:ring-0",
+                "hover:bg-muted/60 hover:text-foreground focus-visible:ring-0 transition-all duration-200",
                 disabled && "opacity-50 cursor-not-allowed",
               )}
             >
@@ -294,10 +294,10 @@ export function OptionsSelector({
               : {}
           }
           className={cn(
-            "bg-popover border rounded-xl shadow-2xl z-[9999] overflow-hidden flex flex-col backdrop-blur-md",
+            "bg-popover/95 backdrop-blur-xl border rounded-xl shadow-2xl z-[9999] overflow-hidden flex flex-col border-border/50",
             isDesktop
               ? "max-h-[55vh] w-full max-w-[440px]"
-              : "fixed bottom-0 left-0 right-0 rounded-b-none w-full max-h-[75vh] animate-in slide-in-from-bottom-2 duration-200",
+              : "fixed bottom-0 left-0 right-0 rounded-b-none w-full max-h-[75vh] animate-in slide-in-from-bottom-2 duration-300 ease-out",
           )}
         >
           {/* Mobile drag handle */}
@@ -308,15 +308,15 @@ export function OptionsSelector({
           )}
           
           {/* Header with tabs */}
-          <div className="border-b border-border/50">
+          <div className="border-b border-border/30 bg-muted/20">
             <div className="flex text-xs sm:text-sm">
               <button
                 onClick={() => setActiveTab("style")}
                 className={cn(
-                  "flex-1 px-3 sm:px-4 py-2.5 sm:py-3 font-medium transition-all flex items-center justify-center gap-1.5 sm:gap-2",
+                  "flex-1 px-3 sm:px-4 py-2.5 sm:py-3 font-medium transition-all duration-200 ease-out flex items-center justify-center gap-1.5 sm:gap-2",
                   activeTab === "style"
                     ? "bg-primary/10 text-primary border-b-2 border-primary"
-                    : "text-muted-foreground hover:text-foreground hover:bg-muted/30",
+                    : "text-muted-foreground hover:text-foreground hover:bg-muted/40 transition-all duration-200",
                 )}
               >
                 <Sparkles size={12} className="sm:h-[14px] sm:w-[14px]" />
@@ -326,10 +326,10 @@ export function OptionsSelector({
                 <button
                   onClick={() => setActiveTab("tools")}
                   className={cn(
-                    "flex-1 px-3 sm:px-4 py-2.5 sm:py-3 font-medium transition-all flex items-center justify-center gap-1.5 sm:gap-2",
+                    "flex-1 px-3 sm:px-4 py-2.5 sm:py-3 font-medium transition-all duration-200 ease-out flex items-center justify-center gap-1.5 sm:gap-2",
                     activeTab === "tools"
                       ? "bg-primary/10 text-primary border-b-2 border-primary"
-                      : "text-muted-foreground hover:text-foreground",
+                      : "text-muted-foreground hover:text-foreground transition-all duration-200",
                   )}
                 >
                   <Wrench size={12} className="sm:h-[14px] sm:w-[14px]" />
@@ -345,10 +345,10 @@ export function OptionsSelector({
                 <button
                   onClick={() => setActiveTab("thinking")}
                   className={cn(
-                    "flex-1 px-3 sm:px-4 py-2.5 sm:py-3 font-medium transition-all flex items-center justify-center gap-1.5 sm:gap-2",
+                    "flex-1 px-3 sm:px-4 py-2.5 sm:py-3 font-medium transition-all duration-200 ease-out flex items-center justify-center gap-1.5 sm:gap-2",
                     activeTab === "thinking"
                       ? "bg-primary/10 text-primary border-b-2 border-primary"
-                      : "text-muted-foreground hover:text-foreground",
+                      : "text-muted-foreground hover:text-foreground transition-all duration-200",
                   )}
                 >
                   <Brain size={12} className="sm:h-[14px] sm:w-[14px]" />
@@ -379,9 +379,9 @@ export function OptionsSelector({
                             if (p.id !== "none") onRecipeChange("");
                           }}
                           className={cn(
-                            "w-full text-left px-2.5 sm:px-3 py-2 sm:py-2.5 rounded-lg flex items-center gap-2.5 sm:gap-3 transition-all",
-                            "hover:bg-muted/50 hover:scale-[1.02] active:scale-[0.98]",
-                            isActive && "bg-primary/10 text-primary ring-1 ring-primary/20",
+                            "w-full text-left px-2.5 sm:px-3 py-2 sm:py-2.5 rounded-lg flex items-center gap-2.5 sm:gap-3 transition-all duration-200",
+                            "hover:bg-muted/60 hover:shadow-sm",
+                            isActive && "bg-primary/10 text-primary ring-1 ring-primary/20 shadow-sm",
                           )}
                         >
                           <span className={cn(
@@ -421,9 +421,9 @@ export function OptionsSelector({
                             if (r.id !== "none") onPersonaChange("");
                           }}
                           className={cn(
-                            "w-full text-left px-2.5 sm:px-3 py-2 sm:py-2.5 rounded-lg flex items-center gap-2.5 sm:gap-3 transition-all",
-                            "hover:bg-muted/50 hover:scale-[1.02] active:scale-[0.98]",
-                            isActive && "bg-primary/10 text-primary ring-1 ring-primary/20",
+                            "w-full text-left px-2.5 sm:px-3 py-2 sm:py-2.5 rounded-lg flex items-center gap-2.5 sm:gap-3 transition-all duration-200",
+                            "hover:bg-muted/60 hover:shadow-sm",
+                            isActive && "bg-primary/10 text-primary ring-1 ring-primary/20 shadow-sm",
                           )}
                         >
                           <span className={cn(
@@ -490,9 +490,9 @@ export function OptionsSelector({
                                 key={tool.id}
                                 onClick={() => onToggleTool(tool.id)}
                                 className={cn(
-                                  "w-full text-left px-3 py-2.5 rounded-lg flex items-center gap-3 transition-all",
-                                  "hover:bg-muted/50",
-                                  isEnabled && "bg-primary/10 text-primary",
+                                  "w-full text-left px-3 py-2.5 rounded-lg flex items-center gap-3 transition-all duration-200",
+                                  "hover:bg-muted/60 hover:shadow-sm",
+                                  isEnabled && "bg-primary/10 text-primary shadow-sm",
                                 )}
                               >
                                 <span className={cn(
@@ -533,9 +533,9 @@ export function OptionsSelector({
                                 key={tool.id}
                                 onClick={() => onToggleTool(tool.id)}
                                 className={cn(
-                                  "w-full text-left px-3 py-2.5 rounded-lg flex items-center gap-3 transition-all",
-                                  "hover:bg-muted/50",
-                                  isEnabled && "bg-primary/10 text-primary",
+                                  "w-full text-left px-3 py-2.5 rounded-lg flex items-center gap-3 transition-all duration-200",
+                                  "hover:bg-muted/60 hover:shadow-sm",
+                                  isEnabled && "bg-primary/10 text-primary shadow-sm",
                                 )}
                               >
                                 <span className={cn(
@@ -576,9 +576,9 @@ export function OptionsSelector({
                                 key={tool.id}
                                 onClick={() => onToggleTool(tool.id)}
                                 className={cn(
-                                  "w-full text-left px-3 py-2.5 rounded-lg flex items-center gap-3 transition-all",
-                                  "hover:bg-muted/50",
-                                  isEnabled && "bg-primary/10 text-primary",
+                                  "w-full text-left px-3 py-2.5 rounded-lg flex items-center gap-3 transition-all duration-200",
+                                  "hover:bg-muted/60 hover:shadow-sm",
+                                  isEnabled && "bg-primary/10 text-primary shadow-sm",
                                 )}
                               >
                                 <span className={cn(
@@ -636,9 +636,9 @@ export function OptionsSelector({
                         key={String(budget)}
                         onClick={() => onThinkingBudgetChange?.(budget)}
                         className={cn(
-                          "w-full text-left px-3 py-2.5 rounded-lg flex items-center gap-3 transition-all",
-                          "hover:bg-muted/50",
-                          isActive && "bg-primary/10 text-primary",
+                          "w-full text-left px-3 py-2.5 rounded-lg flex items-center gap-3 transition-all duration-200",
+                          "hover:bg-muted/60 hover:shadow-sm",
+                          isActive && "bg-primary/10 text-primary shadow-sm",
                         )}
                       >
                         <span className={cn(
diff --git a/src/components/Sidebar.tsx b/src/components/Sidebar.tsx
index 5d69fed..4c93253 100644
--- a/src/components/Sidebar.tsx
+++ b/src/components/Sidebar.tsx
@@ -9,7 +9,7 @@ import {
   Edit2,
   Copy,
   MoreHorizontal,
-  X,
+  PanelLeft,
   Search,
   Sparkles,
   User,
@@ -23,12 +23,10 @@ import {
   ExternalLink,
   Library,
   Loader2,
+  Zap,
 } from "lucide-react";
 import { cn } from "@/lib/utils";
 import { Button } from "@/components/ui/button";
-import { ScrollArea } from "@/components/ui/scroll-area";
-import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
-
 import { 
   DropdownMenu, 
   DropdownMenuContent, 
@@ -46,6 +44,22 @@ import {
 } from "@/components/ui/dialog";
 import { Input } from "@/components/ui/input";
 import { Label } from "@/components/ui/label";
+import { 
+  Sidebar as SidebarRoot, 
+  SidebarContent,
+  SidebarFooter,
+  SidebarGroup,
+  SidebarGroupAction,
+  SidebarGroupContent,
+  SidebarGroupLabel,
+  SidebarHeader,
+  SidebarInput,
+  SidebarMenu,
+  SidebarMenuAction,
+  SidebarMenuButton,
+  SidebarMenuItem,
+  SidebarSeparator
+} from "@/components/ui/sidebar";
 
 function useDebounce<T>(value: T, delay: number) {
   const [debouncedValue, setDebouncedValue] = useState(value);
@@ -62,16 +76,12 @@ interface SidebarProps {
   currentConversationId: Id<"conversations"> | null;
   onSelectConversation: (id: Id<"conversations">) => void;
   onNewConversation: () => void | Promise<void>;
-  isSidebarOpen: boolean;
-  onToggleSidebar: () => void;
 }
 
 export const Sidebar = memo(function Sidebar({
   currentConversationId,
   onSelectConversation,
   onNewConversation,
-  isSidebarOpen,
-  onToggleSidebar,
 }: SidebarProps) {
   const [editingId, setEditingId] = useState<Id<"conversations"> | null>(null);
   const [editTitle, setEditTitle] = useState("");
@@ -101,16 +111,14 @@ export const Sidebar = memo(function Sidebar({
   // Memoize empty args object to avoid unnecessary re-renders
   const convListArgs = useMemo(() => ({}), []);
 
-  const debouncedOpen = useDebounce(isSidebarOpen, 300);
-
-  const {
+  const {  
     results: conversations,
     status,
     loadMore,
   } = usePaginatedQuery(
     api.conversations.listWithMessageCounts,
-    debouncedOpen ? convListArgs : "skip",
-    { initialNumItems: 15 }
+    convListArgs,
+    { initialNumItems: 50 }
   );
 
   const deleteConversation = useMutation(api.conversations.remove);
@@ -363,139 +371,96 @@ export const Sidebar = memo(function Sidebar({
     return sortedFilteredConversations.findIndex((c: any) => c._id === conversationId);
   };
 
-  return (
-    <TooltipProvider>
-      <div
-        className={cn(
-          // Container: distinct sidebar background with subtle depth
-          "h-full flex flex-col w-[300px] relative overflow-hidden border-r border-border/40",
-          "bg-muted/40 backdrop-blur-sm",
-          "before:absolute before:inset-0 before:pointer-events-none",
-          "before:bg-gradient-to-b before:from-muted/20 before:via-transparent before:to-muted/10"
-        )}
-      >
-        {/* Header with close button only on mobile */}
-        <div className="px-3 py-2 flex justify-end lg:hidden">
-          <Button
-            variant="ghost"
-            size="icon"
-            onClick={onToggleSidebar}
-            className="h-8 w-8 hover:bg-muted/50"
-          >
-            <X size={16} />
-          </Button>
-        </div>
+  // Infinite scroll effect
+  useEffect(() => {
+    const scrollArea = document.querySelector('[data-radix-scroll-area-viewport]');
+    if (!scrollArea) return;
 
-        {/* New Chat + Library */}
-        <div className="p-3 flex items-center gap-2">
-          <Button
-            onClick={() => void onNewConversation()}
-            className={cn(
-              "flex-1 justify-start gap-2 h-10 rounded-xl font-semibold",
-              "bg-gradient-to-br from-primary/90 via-primary/80 to-primary/70",
-              "hover:from-primary hover:via-primary/90 hover:to-primary/80",
-              "text-primary-foreground shadow-sm transition-all duration-150"
-            )}
-          >
-            <Plus className="h-4 w-4" />
-            <span className="text-sm">New chat</span>
-          </Button>
-          <Tooltip>
-            <TooltipTrigger asChild>
-              <Button
-                onClick={() => navigateTo("/library")}
-                variant="outline"
-                size="icon"
-                className="h-10 w-10 rounded-xl bg-background/60 hover:bg-muted/60 transition-colors duration-150"
-              >
-                <Library className="h-4 w-4" />
-              </Button>
-            </TooltipTrigger>
-            <TooltipContent side="right">
-              <p>Library</p>
-            </TooltipContent>
-          </Tooltip>
-        </div>
+    const handleScroll = () => {
+      const { scrollTop, scrollHeight, clientHeight } = scrollArea;
+      if (scrollHeight - scrollTop <= clientHeight + 100 && status === "CanLoadMore") {
+        loadMore(25);
+      }
+    };
 
-        {/* Search */}
-        <div className="px-3 pb-3">
-          <div className="relative group">
-            <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground/60 h-4 w-4" />
-            <input
-              type="text"
-              placeholder="Search conversations..."
-              value={searchInput}
-              onChange={(e) => {
-                const val = e.target.value;
-                setSearchInput(val);
-                if (val === "") {
-                  setSearchQuery("");
-                  setHighlightIndex(-1);
-                }
-              }}
-              onKeyDown={(e) => {
-                if (e.key === "Enter") {
-                  const trimmed = searchInput.trim();
-                  const debounceSet = setTimeout(() => {
-                    if (trimmed !== searchQuery) {
-                      setSearchQuery(trimmed);
-                      setHighlightIndex(0);
-                    } else if (highlightIndex >= 0 && highlightIndex < sortedFilteredConversations.length) {
-                      onSelectConversation(sortedFilteredConversations[highlightIndex]._id);
-                    }
-                  }, 300);
-                  return () => clearTimeout(debounceSet);
-                } else if (e.key === "Escape") {
-                  setSearchInput("");
-                  setSearchQuery("");
-                  setHighlightIndex(-1);
-                } else if (e.key === "ArrowDown") {
-                  e.preventDefault();
-                  if (sortedFilteredConversations.length > 0) {
-                    setHighlightIndex((prev) => (prev + 1) % sortedFilteredConversations.length);
-                  }
-                } else if (e.key === "ArrowUp") {
-                  e.preventDefault();
-                  if (sortedFilteredConversations.length > 0) {
-                    setHighlightIndex((prev) => (prev <= 0 ? sortedFilteredConversations.length - 1 : prev - 1));
-                  }
-                }
-              }}
-              className={cn(
-                "w-full pl-9 pr-3 py-2.5 text-sm rounded-xl",
-                "bg-background/60 border border-border/40",
-                "focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary/40",
-                "placeholder:text-muted-foreground/60 transition-all duration-150"
-              )}
-            />
+    scrollArea.addEventListener('scroll', handleScroll);
+    return () => scrollArea.removeEventListener('scroll', handleScroll);
+  }, [status, loadMore]);
+
+  return (
+    <>
+      <SidebarRoot>
+      <SidebarHeader className="border-b border-sidebar-border">
+        <div className="flex items-center justify-between p-4">
+          <div className="flex items-center gap-3">
+            <div className="flex items-center justify-center w-9 h-9 rounded-xl bg-sidebar-primary/10">
+              <Zap className="h-5 w-5 text-sidebar-primary" />
+            </div>
+            <span className="font-semibold text-sidebar-foreground text-sm">Erzen AI</span>
           </div>
         </div>
-
-        {/* Conversations (Grouped & simplified) */}
-        <ScrollArea className="flex-1 px-3">
-          <div className="pb-2 space-y-2">
-            {groupedConversations.map(({ label, items }) => (
-              <div key={label} className="space-y-0.5">
-                <div className="px-2 flex items-center justify-between">
-                  <div className="text-label-sm text-muted-foreground/70">{label}</div>
-                  <div className="text-[10px] text-muted-foreground/60 bg-muted/30 px-1.5 py-0.5 rounded-full">{items.length}</div>
-                </div>
-                {items.map((conversation: any) => {
+      </SidebarHeader>
+
+      <SidebarContent>
+        <SidebarGroup>
+          <SidebarGroupContent>
+            <SidebarMenu>
+              <SidebarMenuItem>
+                <SidebarMenuButton 
+                  onClick={() => void onNewConversation()}
+                  className="w-full justify-start gap-2 h-9"
+                >
+                  <Plus className="h-4 w-4" />
+                  <span className="text-sm font-medium">New chat</span>
+                </SidebarMenuButton>
+              </SidebarMenuItem>
+              <SidebarMenuItem>
+                <SidebarMenuButton 
+                  onClick={() => {
+                    // Trigger command palette (same as Cmd+K)
+                    const event = new KeyboardEvent('keydown', {
+                      key: 'k',
+                      ctrlKey: true,
+                      metaKey: true,
+                      bubbles: true
+                    });
+                    document.dispatchEvent(event);
+                  }}
+                  className="w-full justify-start gap-2 h-9"
+                >
+                  <Search className="h-4 w-4" />
+                  <span className="text-sm font-medium">Search chats</span>
+                </SidebarMenuButton>
+              </SidebarMenuItem>
+              <SidebarMenuItem>
+                <SidebarMenuButton 
+                  onClick={() => navigateTo("/library")}
+                  className="w-full justify-start gap-2 h-9"
+                >
+                  <Library className="h-4 w-4" />
+                  <span className="text-sm font-medium">Library</span>
+                </SidebarMenuButton>
+              </SidebarMenuItem>
+            </SidebarMenu>
+          </SidebarGroupContent>
+        </SidebarGroup>
+
+        <SidebarSeparator />
+
+        <SidebarGroup className="flex-1">
+          <SidebarGroupLabel>Chats</SidebarGroupLabel>
+          <SidebarGroupContent className="flex-1">
+            <div className="overflow-y-auto flex-1">
+              <SidebarMenu>
+                {sortedFilteredConversations.map((conversation: any) => {
                   const globalIndex = getGlobalIndex(conversation._id);
+                  const isActive = currentConversationId === conversation._id;
+                  
                   return (
-                    <div
-                      key={conversation._id}
-                      className={cn(
-                        "group relative rounded-sm overflow-hidden transition-all duration-150",
-                        (currentConversationId === conversation._id || globalIndex === highlightIndex)
-                          ? "bg-primary/12"
-                          : "hover:bg-muted/40"
-                      )}
-                    >
+                    <SidebarMenuItem key={conversation._id}>
                       {editingId === conversation._id ? (
                         <div className="p-2">
-                          <input
-                            type="text"
+                          <Input
                             value={editTitle}
                             onChange={(e) => setEditTitle(e.target.value)}
                             onKeyDown={(e) => {
@@ -503,310 +468,287 @@ export const Sidebar = memo(function Sidebar({
                               if (e.key === "Escape") handleCancelEdit();
                             }}
                             onBlur={() => void handleSaveEdit()}
-                            className="w-full px-2.5 py-2 text-sm border border-primary/40 rounded-lg bg-background focus:outline-none focus:ring-2 focus:ring-primary/30 font-medium"
+                            className="text-sm"
                             autoFocus
                           />
                         </div>
                       ) : (
-                        <div className="relative flex w-full h-9 group">
-                          {/* Main conversation button */}
-                          <div
+                        <>
+                          <SidebarMenuButton 
+                            isActive={isActive}
                             onClick={() => {
                               onSelectConversation(conversation._id);
                               setHighlightIndex(globalIndex);
                             }}
-                            className="flex-1 flex items-center gap-1 px-2 cursor-pointer hover:bg-muted/50 rounded-lg transition-colors"
+                            className="group w-full justify-start gap-2 pr-8"
                           >
-                            {/* Left: pin/spinner + title (truncate) */}
-                            <div className="flex min-w-0 flex-1 items-center gap-1">
+                            <div className="flex min-w-0 flex-1 items-center gap-2">
                               {conversation.isGenerating && (
-                                <Loader2 size={11} className="text-primary animate-spin shrink-0" />
+                                <Loader2 size={12} className="text-sidebar-primary animate-spin shrink-0" />
                               )}
                               {conversation.isPinned && (
-                                <Pin size={10} className="text-muted-foreground/70 shrink-0" />
+                                <Pin size={10} className="text-sidebar-foreground/70 shrink-0" />
                               )}
-                              <p
-                                className={cn(
-                                  "text-sm font-normal text-left leading-tight overflow-hidden text-ellipsis whitespace-nowrap",
-                                  currentConversationId === conversation._id 
-                                    ? "text-primary" 
-                                    : "text-foreground/90 group-hover:text-foreground"
+                              <span className="text-sm truncate" title={conversation.title}>
+                                {conversation.title}
+                              </span>
+                            </div>
+                          </SidebarMenuButton>
+                          
+                          <DropdownMenu>
+                            <DropdownMenuTrigger asChild>
+                              <SidebarMenuAction showOnHover>
+                                <MoreHorizontal size={16} />
+                              </SidebarMenuAction>
+                            </DropdownMenuTrigger>
+                            <DropdownMenuContent side="right" align="start" className="w-48">
+                              <DropdownMenuItem onClick={() => void handleTogglePin(conversation._id)}>
+                                {conversation.isPinned ? (
+                                  <><PinOff className="mr-2 h-4 w-4" />Unpin</>
+                                ) : (
+                                  <><Pin className="mr-2 h-4 w-4" />Pin</>
                                 )}
-                                title={conversation.title}
-                                style={{ maxWidth: '240px', display: 'inline-block', verticalAlign: 'middle', letterSpacing: '0.01em' }}
+                              </DropdownMenuItem>
+                              <DropdownMenuSeparator />
+                              <DropdownMenuItem onClick={() => handleEdit(conversation)}>
+                                <Edit2 className="mr-2 h-4 w-4" />Rename
+                              </DropdownMenuItem>
+                              <DropdownMenuItem onClick={() => void handleGenerateTitle(conversation._id)}>
+                                <Sparkles className="mr-2 h-4 w-4" />Generate Title
+                              </DropdownMenuItem>
+                              <DropdownMenuSeparator />
+                              <DropdownMenuItem onClick={() => void handleDuplicate(conversation._id)}>
+                                <Copy className="mr-2 h-4 w-4" />Duplicate
+                              </DropdownMenuItem>
+                              <DropdownMenuItem onClick={() => void handleShare(conversation._id)}>
+                                <Share2 className="mr-2 h-4 w-4" />Share
+                              </DropdownMenuItem>
+                              <DropdownMenuItem onClick={() => void handleExport(conversation._id)}>
+                                <Download className="mr-2 h-4 w-4" />Export
+                              </DropdownMenuItem>
+                              <DropdownMenuSeparator />
+                              <DropdownMenuItem 
+                                onClick={() => openDeleteDialog(conversation._id)} 
+                                className="text-destructive focus:text-destructive"
                               >
-                                {truncateTitle(conversation.title, 30)}
-                              </p>
-                            </div>
-                          </div>
-
-                          {/* Separate dropdown button */}
-                          <div className="shrink-0 flex items-center">
-                            <DropdownMenu>
-                              <DropdownMenuTrigger asChild>
-                                <button 
-                                  className="h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity hover:bg-muted/60 rounded flex items-center justify-center"
-                                >
-                                  <MoreHorizontal size={12} />
-                                </button>
-                              </DropdownMenuTrigger>
-                                <DropdownMenuContent align="end" className="w-48 bg-popover/95 backdrop-blur-md border border-border/60 shadow-lg">
-                                  <DropdownMenuItem onClick={() => void handleTogglePin(conversation._id)}>
-                                    {conversation.isPinned ? (<><PinOff className="mr-2 h-4 w-4" />Unpin</>) : (<><Pin className="mr-2 h-4 w-4" />Pin</>)}
-                                  </DropdownMenuItem>
-                                  <DropdownMenuSeparator />
-                                  <DropdownMenuItem onClick={() => handleEdit(conversation)}><Edit2 className="mr-2 h-4 w-4" />Rename</DropdownMenuItem>
-                                  <DropdownMenuItem onClick={() => void handleGenerateTitle(conversation._id)}><Sparkles className="mr-2 h-4 w-4" />Generate Title</DropdownMenuItem>
-                                  <DropdownMenuSeparator />
-                                  <DropdownMenuItem onClick={() => void handleDuplicate(conversation._id)}><Copy className="mr-2 h-4 w-4" />Duplicate</DropdownMenuItem>
-                                  <DropdownMenuItem onClick={() => void handleShare(conversation._id)}><Share2 className="mr-2 h-4 w-4" />Share</DropdownMenuItem>
-                                  <DropdownMenuItem onClick={() => void handleExport(conversation._id)}><Download className="mr-2 h-4 w-4" />Export</DropdownMenuItem>
-                                  <DropdownMenuSeparator />
-                                  <DropdownMenuItem onClick={() => openDeleteDialog(conversation._id)} className="text-destructive focus:text-destructive"><Trash2 className="mr-2 h-4 w-4" />Delete</DropdownMenuItem>
-                                </DropdownMenuContent>
-                              </DropdownMenu>
-                            </div>
-                        </div>
+                                <Trash2 className="mr-2 h-4 w-4" />Delete
+                              </DropdownMenuItem>
+                            </DropdownMenuContent>
+                          </DropdownMenu>
+                        </>
                       )}
-                    </div>
+                    </SidebarMenuItem>
                   );
                 })}
-              </div>
-            ))}
-
-            {groupedConversations.length === 0 && (
-              <div className="text-center text-muted-foreground p-6 space-y-3">
-                <div className="w-12 h-12 rounded-full bg-muted/40 flex items-center justify-center mx-auto">
-                  <MessageSquare size={16} className="text-muted-foreground/60" />
-                </div>
-                <div className="space-y-1">
-                  <p className="text-body-md">{searchQuery ? "No matching conversations" : "No conversations yet"}</p>
-                  <p className="text-xs text-muted-foreground/70">{searchQuery ? "Try adjusting your search terms" : "Start a new chat to begin your conversation"}</p>
-                </div>
-              </div>
-            )}
-
-            {status === "CanLoadMore" && (
-              <div className="py-3">
-                <Button 
-                  variant="ghost" 
-                  onClick={() => loadMore(15)} 
-                  className="justify-start h-9 text-sm text-muted-foreground hover:text-foreground hover:bg-muted/50 transition-all duration-150 group w-full"
-                >
-                  <ChevronDown size={14} className="mr-2 group-hover:translate-y-0.5 transition-transform duration-150" />
-                  Load more conversations
-                </Button>
-              </div>
-            )}
-          </div>
-        </ScrollArea>
-
-        {/* Delete Dialog */}
-        <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
-          <DialogContent className="sm:max-w-md">
-            <DialogHeader>
-              <DialogTitle className="flex items-center gap-2">
-                <Trash2 size={18} className="text-destructive" />
-                Delete Conversation
-              </DialogTitle>
-              <DialogDescription>
-                This action cannot be undone. This will permanently delete the conversation and all its messages.
-              </DialogDescription>
-            </DialogHeader>
-            <DialogFooter>
-              <Button variant="outline" onClick={() => setDeleteDialogOpen(false)}>
-                Cancel
-              </Button>
-              <Button variant="destructive" onClick={() => void handleDelete()}>
-                Delete
-              </Button>
-            </DialogFooter>
-          </DialogContent>
-        </Dialog>
-
-        {/* Share Dialog */}
-        <Dialog open={shareDialogOpen} onOpenChange={setShareDialogOpen}>
-          <DialogContent className="sm:max-w-md">
-            <DialogHeader>
-              <DialogTitle className="flex items-center gap-2">
-                <Share2 size={18} className="text-primary" />
-                Share Conversation
-              </DialogTitle>
-              <DialogDescription>
-                Anyone with this link can view the conversation (read-only).
-              </DialogDescription>
-            </DialogHeader>
-            <div className="space-y-4">
-              <div className="space-y-2">
-                <Label htmlFor="share-url">Share URL</Label>
-                <div className="flex gap-2">
-                  <Input
-                    id="share-url"
-                    value={shareUrl}
-                    readOnly
-                    className="font-mono text-sm"
-                  />
-                  <Button
-                    size="icon"
-                    variant="outline"
-                    onClick={() => void navigator.clipboard.writeText(shareUrl)}
-                  >
-                    <Copy size={16} />
-                  </Button>
-                  <Button
-                    size="icon"
-                    variant="outline"
-                    onClick={() => window.open(shareUrl, '_blank')}
-                  >
-                    <ExternalLink size={16} />
-                  </Button>
-                </div>
-              </div>
-            </div>
-            <DialogFooter>
-              <Button variant="outline" onClick={() => setShareDialogOpen(false)}>
-                Close
-              </Button>
-              <Button 
-                variant="destructive" 
-                onClick={() => activeConversationId && void handleUnshare(activeConversationId)}
-              >
-                Revoke Share
-              </Button>
-            </DialogFooter>
-          </DialogContent>
-        </Dialog>
-
-        {/* Export Dialog */}
-        <Dialog open={exportDialogOpen} onOpenChange={setExportDialogOpen}>
-          <DialogContent className="sm:max-w-md">
-            <DialogHeader>
-              <DialogTitle className="flex items-center gap-2">
-                <Download size={18} className="text-primary" />
-                Export Conversation
-              </DialogTitle>
-              <DialogDescription>
-                Download the conversation as a JSON file that can be imported later.
-              </DialogDescription>
-            </DialogHeader>
-            <div className="space-y-4">
-              {exportData ? (
-                <div className="space-y-2">
-                  <p className="text-sm text-muted-foreground">
-                    Export includes {exportData.messages.length} messages and {exportData.branches.length} branches.
-                  </p>
-                  <div className="p-3 bg-muted rounded-lg">
-                    <p className="text-sm font-medium">Ready to download:</p>
-                    <p className="text-xs text-muted-foreground">
-                      {exportData.conversation.title} • {new Date(exportData.exportedAt).toLocaleDateString()}
+              </SidebarMenu>
+              
+              {sortedFilteredConversations.length === 0 && (
+                <div className="text-center text-sidebar-foreground/70 p-6 space-y-3">
+                  <div className="w-12 h-12 rounded-full bg-sidebar-accent flex items-center justify-center mx-auto">
+                    <MessageSquare size={16} className="text-sidebar-foreground/60" />
+                  </div>
+                  <div className="space-y-1">
+                    <p className="text-sm">{searchQuery ? "No matching conversations" : "No conversations yet"}</p>
+                    <p className="text-xs text-sidebar-foreground/60">
+                      {searchQuery ? "Try adjusting your search terms" : "Start a new chat to begin your conversation"}
                     </p>
                   </div>
                 </div>
-              ) : (
-                <div className="text-center py-4">
-                  <div className="animate-spin w-6 h-6 border-2 border-primary border-t-transparent rounded-full mx-auto" />
-                  <p className="text-sm text-muted-foreground mt-2">Preparing export...</p>
+              )}
+
+              {status === "LoadingMore" && (
+                <div className="py-3 flex justify-center">
+                  <Loader2 size={16} className="animate-spin text-sidebar-foreground/70" />
                 </div>
               )}
             </div>
-            <DialogFooter>
-              <Button variant="outline" onClick={() => setExportDialogOpen(false)}>
-                Cancel
-              </Button>
-              <Button 
-                onClick={() => void handleDownloadExport()}
-                disabled={!exportData}
-              >
-                <Download size={16} className="mr-2" />
-                Download
-              </Button>
-            </DialogFooter>
-          </DialogContent>
-        </Dialog>
-
-        {/* User Profile - Bottom */}
+          </SidebarGroupContent>
+        </SidebarGroup>
+
+      </SidebarContent>
+
+      <SidebarFooter className="border-t border-sidebar-border">
         {user && (
-          <div className="border-t border-border/30 bg-background/95 backdrop-blur-xl">
-            <div className="flex items-center gap-3 p-4">
-              {user.image ? (
-                <img
-                  src={user.image}
-                  alt="Avatar"
-                  className="w-10 h-10 rounded-full object-cover border border-border/50"
-                />
-              ) : (
-                <div className="w-10 h-10 rounded-full bg-muted/60 flex items-center justify-center border border-border/50">
-                  <User size={16} className="text-muted-foreground/80" />
-                </div>
-              )}
-              <div className="flex-1 min-w-0">
-                <div className="flex items-center gap-2">
-                  <p
-                    className={cn(
-                      "text-sm font-semibold truncate text-foreground",
-                      preferences?.hideUserInfo && "blur-[3px] select-none"
-                    )}
-                  >
-                    {user.name ?? user.email}
-                  </p>
-                  {usage?.plan && (
-                    <Tooltip>
-                      <TooltipTrigger asChild>
-                        <span className={cn(
-                          "px-2 py-0.5 text-[10px] font-bold uppercase tracking-wider rounded-full shrink-0 cursor-help transition-all duration-150 hover:scale-105",
-                          usage.plan === "free" ? "bg-slate-100 text-slate-700 dark:bg-slate-800/80 dark:text-slate-300 border border-slate-200 dark:border-slate-700" :
-                          usage.plan === "pro" ? "bg-gradient-to-r from-blue-100 to-blue-50 text-blue-800 dark:from-blue-900/60 dark:to-blue-800/40 dark:text-blue-200 border border-blue-200 dark:border-blue-700 shadow-sm" :
-                          usage.plan === "ultra" ? "bg-gradient-to-r from-purple-100 to-purple-50 text-purple-800 dark:from-purple-900/60 dark:to-purple-800/40 dark:text-purple-200 border border-purple-200 dark:border-purple-700 shadow-sm" :
-                          "bg-gradient-to-r from-amber-100 to-yellow-50 text-amber-800 dark:from-amber-900/60 dark:to-yellow-800/40 dark:text-amber-200 border border-amber-200 dark:border-amber-700 shadow-sm"
-                        )}>
-                          {usage.plan}
-                        </span>
-                      </TooltipTrigger>
-                      <TooltipContent side="top">
-                        <p className="font-medium">
-                          {usage.plan === "free" ? "Free Plan" : 
-                           usage.plan === "pro" ? "Pro Plan" :
-                           usage.plan === "ultra" ? "Ultra Plan" : "Max Plan"}
-                        </p>
-                        <p className="text-xs text-muted-foreground">
-                          {usage.creditsUsed.toLocaleString()} / {usage.creditsLimit.toLocaleString()} credits used
-                        </p>
-                      </TooltipContent>
-                    </Tooltip>
-                  )}
-                </div>
+          <div className="flex items-center gap-3 p-4">
+            {user.image ? (
+              <img
+                src={user.image}
+                alt="Avatar"
+                className="w-8 h-8 rounded-full object-cover border border-sidebar-border"
+              />
+            ) : (
+              <div className="w-8 h-8 rounded-full bg-sidebar-accent flex items-center justify-center border border-sidebar-border">
+                <User size={14} className="text-sidebar-foreground/80" />
+              </div>
+            )}
+            <div className="flex-1 min-w-0">
+              <div className="flex items-center gap-2">
                 <p
                   className={cn(
-                    "text-xs text-muted-foreground/80 truncate",
+                    "text-sm font-medium truncate text-sidebar-foreground",
                     preferences?.hideUserInfo && "blur-[3px] select-none"
                   )}
                 >
-                  {user.email}
+                  {user.name ?? user.email}
                 </p>
+                {usage?.plan && (
+                  <span className={cn(
+                    "px-1.5 py-0.5 text-[9px] font-bold uppercase tracking-wider rounded-md shrink-0",
+                    usage.plan === "free" ? "bg-sidebar-accent text-sidebar-foreground" :
+                    usage.plan === "pro" ? "bg-blue-100 text-blue-800 dark:bg-blue-900/40 dark:text-blue-300" :
+                    usage.plan === "ultra" ? "bg-purple-100 text-purple-800 dark:bg-purple-900/40 dark:text-purple-300" :
+                    "bg-amber-100 text-amber-800 dark:bg-amber-900/40 dark:text-amber-300"
+                  )}>
+                    {usage.plan}
+                  </span>
+                )}
               </div>
-              <div className="flex items-center gap-1">
-                <Tooltip>
-                  <TooltipTrigger asChild>
-                    <Button
-                      variant="ghost"
-                      size="icon"
-                      onClick={() => void toggleUserInfoVisibility()}
-                      className="h-8 w-8 hover:bg-muted/60 transition-colors"
-                      title={preferences?.hideUserInfo ? "Show user info" : "Hide user info"}
-                    >
-                      {preferences?.hideUserInfo ? <EyeOff size={14} /> : <Eye size={14} />}
-                    </Button>
-                  </TooltipTrigger>
-                  <TooltipContent side="top">
-                    <p>{preferences?.hideUserInfo ? "Show user info" : "Hide user info"}</p>
-                  </TooltipContent>
-                </Tooltip>
-              </div>
+              <p
+                className={cn(
+                  "text-xs text-sidebar-foreground/70 truncate",
+                  preferences?.hideUserInfo && "blur-[3px] select-none"
+                )}
+              >
+                {user.email}
+              </p>
             </div>
+            <Button
+              variant="ghost"
+              size="icon"
+              onClick={() => void toggleUserInfoVisibility()}
+              className="h-6 w-6 hover:bg-sidebar-accent text-sidebar-foreground/70"
+            >
+              {preferences?.hideUserInfo ? <EyeOff size={12} /> : <Eye size={12} />}
+            </Button>
           </div>
         )}
+      </SidebarFooter>
 
-    </div>
-    </TooltipProvider>
+      </SidebarRoot>
+    
+      {/* Dialogs remain outside the Sidebar but within the component */}
+      {/* Delete Dialog */}
+      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
+        <DialogContent className="sm:max-w-md">
+          <DialogHeader>
+            <DialogTitle className="flex items-center gap-2">
+              <Trash2 size={18} className="text-destructive" />
+              Delete Conversation
+            </DialogTitle>
+            <DialogDescription>
+              This action cannot be undone. This will permanently delete the conversation and all its messages.
+            </DialogDescription>
+          </DialogHeader>
+          <DialogFooter>
+            <Button variant="outline" onClick={() => setDeleteDialogOpen(false)}>
+              Cancel
+            </Button>
+            <Button variant="destructive" onClick={() => void handleDelete()}>
+              Delete
+            </Button>
+          </DialogFooter>
+        </DialogContent>
+      </Dialog>
+
+      {/* Share Dialog */}
+      <Dialog open={shareDialogOpen} onOpenChange={setShareDialogOpen}>
+        <DialogContent className="sm:max-w-md">
+          <DialogHeader>
+            <DialogTitle className="flex items-center gap-2">
+              <Share2 size={18} className="text-primary" />
+              Share Conversation
+            </DialogTitle>
+            <DialogDescription>
+              Anyone with this link can view the conversation (read-only).
+            </DialogDescription>
+          </DialogHeader>
+          <div className="space-y-4">
+            <div className="space-y-2">
+              <Label htmlFor="share-url">Share URL</Label>
+              <div className="flex gap-2">
+                <Input
+                  id="share-url"
+                  value={shareUrl}
+                  readOnly
+                  className="font-mono text-sm"
+                />
+                <Button
+                  size="icon"
+                  variant="outline"
+                  onClick={() => void navigator.clipboard.writeText(shareUrl)}
+                >
+                  <Copy size={16} />
+                </Button>
+                <Button
+                  size="icon"
+                  variant="outline"
+                  onClick={() => window.open(shareUrl, '_blank')}
+                >
+                  <ExternalLink size={16} />
+                </Button>
+              </div>
+            </div>
+          </div>
+          <DialogFooter>
+            <Button variant="outline" onClick={() => setShareDialogOpen(false)}>
+              Close
+            </Button>
+            <Button 
+              variant="destructive" 
+              onClick={() => activeConversationId && void handleUnshare(activeConversationId)}
+            >
+              Revoke Share
+            </Button>
+          </DialogFooter>
+        </DialogContent>
+      </Dialog>
+
+      {/* Export Dialog */}
+      <Dialog open={exportDialogOpen} onOpenChange={setExportDialogOpen}>
+        <DialogContent className="sm:max-w-md">
+          <DialogHeader>
+            <DialogTitle className="flex items-center gap-2">
+              <Download size={18} className="text-primary" />
+              Export Conversation
+            </DialogTitle>
+            <DialogDescription>
+              Download the conversation as a JSON file that can be imported later.
+            </DialogDescription>
+          </DialogHeader>
+          <div className="space-y-4">
+            {exportData ? (
+              <div className="space-y-2">
+                <p className="text-sm text-muted-foreground">
+                  Export includes {exportData.messages.length} messages and {exportData.branches.length} branches.
+                </p>
+                <div className="p-3 bg-muted rounded-lg">
+                  <p className="text-sm font-medium">Ready to download:</p>
+                  <p className="text-xs text-muted-foreground">
+                    {exportData.conversation.title} • {new Date(exportData.exportedAt).toLocaleDateString()}
+                  </p>
+                </div>
+              </div>
+            ) : (
+              <div className="text-center py-4">
+                <div className="animate-spin w-6 h-6 border-2 border-primary border-t-transparent rounded-full mx-auto" />
+                <p className="text-sm text-muted-foreground mt-2">Preparing export...</p>
+              </div>
+            )}
+          </div>
+          <DialogFooter>
+            <Button variant="outline" onClick={() => setExportDialogOpen(false)}>
+              Cancel
+            </Button>
+            <Button 
+              onClick={() => void handleDownloadExport()}
+              disabled={!exportData}
+            >
+              <Download size={16} className="mr-2" />
+              Download
+            </Button>
+          </DialogFooter>
+        </DialogContent>
+      </Dialog>
+    </>
   );
 });
\ No newline at end of file
