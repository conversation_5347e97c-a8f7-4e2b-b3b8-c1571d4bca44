Stack trace:
Frame         Function      Args
0007FFFFB750  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFFA650) msys-2.0.dll+0x2118E
0007FFFFB750  0002100469BA (000000000000, 000000000000, 000000000000, 0007FFFFBA28) msys-2.0.dll+0x69BA
0007FFFFB750  0002100469F2 (00021028DF99, 0007FFFFB608, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFFB750  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFFB750  00021006A545 (0007FFFFB760, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0007FFFFBA30  00021006B9A5 (0007FFFFB760, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFE89230000 ntdll.dll
7FFE383E0000 aswhook.dll
7FFE883C0000 KERNEL32.DLL
7FFE86600000 KERNELBASE.dll
7FFE82700000 apphelp.dll
7FFE88BA0000 USER32.dll
7FFE86320000 win32u.dll
000210040000 msys-2.0.dll
7FFE87590000 GDI32.dll
7FFE86D60000 gdi32full.dll
7FFE86C40000 msvcp_win.dll
7FFE86B20000 ucrtbase.dll
7FFE87840000 advapi32.dll
7FFE88AE0000 msvcrt.dll
7FFE881B0000 sechost.dll
7FFE86E90000 bcrypt.dll
7FFE890D0000 RPCRT4.dll
7FFE85BA0000 CRYPTBASE.DLL
7FFE869E0000 bcryptPrimitives.dll
7FFE88270000 IMM32.DLL
